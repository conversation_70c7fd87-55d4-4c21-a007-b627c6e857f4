using System.Security.Cryptography;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using ONS.SINapse.Integracao.Sage.Shared.Extensions;
using ONS.SINapse.Integracao.Sage.Shared.Identity;

namespace ONS.SINapse.Integracao.Sage.CrossCutting;

public static class AuthorizationConfiguration
{
    public static void AddSinapseAuthorization(this IServiceCollection services, IConfiguration configuration)
    {
        IConfigurationSection jwtConfiguration = configuration.GetSection("ONS:Authorization");

        services.Configure<AuthorizationServiceSettings>(jwtConfiguration);
        AuthorizationServiceSettings? jwtTokenSettings = jwtConfiguration.Get<AuthorizationServiceSettings>();
        ArgumentNullException.ThrowIfNull(jwtTokenSettings);

        var authorizationOptions = new AuthorizationServiceOptions
        {
            Issuer = jwtTokenSettings.Issuer,
            Audiences = [jwtTokenSettings.Audience],
            UseRsa = jwtTokenSettings.UseRsa,
            RsaPublicKeyExponent64 = jwtTokenSettings.RsaPublicExponent,
            RsaPublicKeyModulus64 = jwtTokenSettings.RsaModulus
        };

        TokenValidationParameters tokenValidationParameters = BuildTokenValidationParameters(authorizationOptions);
        services
            .AddAuthentication("Bearer")
            .AddJwtBearer(optionsToken =>
            {
                optionsToken.TokenValidationParameters = tokenValidationParameters;
                optionsToken.Events = new JwtBearerEvents
                {
                    OnMessageReceived = ValidarTokenAsync,
                    OnTokenValidated = (context) =>
                    {
                        context.HttpContext.Items["User"] = context.Principal;
                        return Task.CompletedTask;
                    }
                };
            });

        services.AddSingleton<IAuthorizationHandler, ClaimAuthorizeHandler>();

        services.AddScoped<IUserContext, UserContext>();

        //var rulesOperationsConfig = new List<RuleOperations>();
        //configuration.GetSection(nameof(RuleOperations)).Bind(rulesOperationsConfig);

        //services.AddSingleton<IRulesOperations, RulesOperations>(_ => new RulesOperations(rulesOperationsConfig));

        //services.AddScoped<IUserContext, UserContext>(provider =>
        //{
        //    var accessor = provider.GetRequiredService<IHttpContextAccessor>();
        //    var rules = provider.GetRequiredService<IRulesOperations>();
        //    return (UserContext.UserContextFactory.Create(accessor, rules) as UserContext)!;
        //});
    }

    /// <summary>
    /// Usado para validar o ‘token’ mesmo vindo da query na requisição http, muito comum em conexão via websocket
    /// </summary>
    /// <param name="context">Mensagem de um evento jwt</param>
    private static Task ValidarTokenAsync(MessageReceivedContext context)
    {
        if (!string.IsNullOrEmpty(context.Token))
        {
            return Task.CompletedTask;
        }

        string? token = context.Request.GetTokenFromAuthorizationHeaderOrQuery();
        if (!string.IsNullOrEmpty(token))
        {
            context.Token = token;
        }

        return Task.CompletedTask;
    }


    private static SecurityKey BuildSecurityKey(AuthorizationServiceOptions options)
    {
        if (!options.UseRsa)
        {
            return new SymmetricSecurityKey(Convert.FromBase64String(options.Secret));
        }

        byte[] modulus = Convert.FromBase64String(options.RsaPublicKeyModulus64);
        byte[] exponent = Convert.FromBase64String(options.RsaPublicKeyExponent64);

        var rsaParameters = new RSAParameters
        {
            Modulus = modulus,
            Exponent = exponent
        };

        return new RsaSecurityKey(rsaParameters);
    }

    private static TokenValidationParameters BuildTokenValidationParameters(
        AuthorizationServiceOptions options)
    {
        SecurityKey securityKey = BuildSecurityKey(options);
        return new TokenValidationParameters()
        {
            ClockSkew = TimeSpan.Zero,
            ValidIssuer = options.Issuer,
            ValidAudiences = options.Audiences,
            IssuerSigningKey = securityKey
        };
    }
}
