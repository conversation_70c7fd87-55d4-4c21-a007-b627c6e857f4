using Confluent.Kafka;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Sage.Business;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Handlers;
using ONS.SINapse.Integracao.Sage.Shared.Diagnostics.Kafka;
using ONS.SINapse.Integracao.Sage.Shared.Messages;
using ONS.SINapse.Integracao.Sage.Shared.Observer;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using ONS.SINapse.Integracao.Sage.Shared.TracingFilter;

namespace ONS.SINapse.Integracao.Sage.CrossCutting;

/// <summary>
/// Classe de extensão responsável pela injeção de dependências e configuração do MassTransit utilizando Amazon SQS como transporte.
/// </summary>
public static class MassTransitInjector
{
    /// <summary>
    /// Adiciona e configura o MassTransit utilizando o transporte Amazon Kafka.
    /// </summary>
    /// <param name="services">Coleção de serviços da aplicação.</param>
    /// <param name="configuration">Instância da configuração da aplicação.</param>
    /// <param name="configureConsumers">Ação opcional para registrar consumidores específicos.</param>
    /// <param name="configureBus">Ação opcional para configuração adicional do barramento.</param>
    /// <returns>Instância modificada de <see cref="IServiceCollection"/> com o MassTransit configurado.</returns>
    /// <exception cref="InvalidOperationException">Lançada quando as configurações Kafka não são encontradas.</exception>
    public static IServiceCollection AddMassTransitWithKafka(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<IRiderRegistrationConfigurator>? configureConsumers = null,
        Action<IRiderRegistrationConfigurator>? configureProducers = null,
        Action<IRiderRegistrationContext, IKafkaFactoryConfigurator>? configureBus = null)
    {
        KafkaSettings kafkaSettings = configuration.GetSection(nameof(KafkaSettings)).Get<KafkaSettings>()
            ?? throw new InvalidOperationException("As configurações Kafka estão ausentes.");

        services.AddSingleton<KafkaSendObserver>();

        services.AddMassTransit(x =>
        {
            x.UsingInMemory((context, cfg) =>
            {
                cfg.ConfigureEndpoints(context);
            });

            x.AddConfigureEndpointsCallback((name, cfg) =>
            {
                cfg.UseMessageRetry(r => r.None());
            });

            x.AddRider(rider =>
            {
                configureConsumers?.Invoke(rider);
                configureProducers?.Invoke(rider);

                rider.UsingKafka((context, k) =>
                {
                    k.Host(kafkaSettings.BootstrapServers, h =>
                    {
                        if (kafkaSettings.RequireAuth)
                        {
                            h.UseSsl(ssl =>
                            {
                                ssl.SslCaPem = SanitizarCertificado(kafkaSettings.SslCaPem);
                                ssl.KeystorePassword = kafkaSettings.SslKeystorePassword;
                            });

                            h.UseSasl(sasl =>
                            {
                                sasl.Mechanism = Enum.Parse<SaslMechanism>(kafkaSettings.SaslMechanism);
                                sasl.Username = kafkaSettings.SaslUsername;
                                sasl.Password = kafkaSettings.SaslPassword;
                                sasl.SecurityProtocol = Enum.Parse<SecurityProtocol>(kafkaSettings.SecurityProtocol);
                            });
                        }
                    });

                    k.ConnectSendObserver(context.GetRequiredService<KafkaSendObserver>());

                    configureBus?.Invoke(context, k);
                });
            });

            services.AddMediator(options =>
            {
                options.AddConsumers(typeof(CommandHandler<>).Assembly, typeof(BusinessApplicationReference).Assembly);
                options.AddRequestClient<Command>();
                options.SetDefaultRequestTimeout(TimeSpan.FromSeconds(60));
            });
        });

        return services;
    }

    /// <summary>
    /// Registra os consumidores de eventos utilizados na aplicação.
    /// </summary>
    /// <returns>Ação que registra os consumidores com o <see cref="IRiderRegistrationConfigurator"/>.</returns>
    public static Action<IRiderRegistrationConfigurator> ConfigureConsumers()
    {
        return x =>
        {
            x.AddConsumer<CadastrarSolicitacaoEventHandler>();
        };
    }

    /// <summary>
    /// Registra os produtores de eventos utilizados na aplicação.
    /// </summary>
    /// <returns>Ação que registra os produtores com o <see cref="IRiderRegistrationConfigurator"/>.</returns>
    public static Action<IRiderRegistrationConfigurator> ConfigureProducers(IConfiguration configuration)
    {
        return x =>
        {
            KafkaQueueSettings queueSettings = configuration.GetSection(nameof(KafkaQueueSettings))
                .Get<KafkaQueueSettings>()
                ?? throw new InvalidOperationException("As configurações das filas Kafka estão ausentes.");

            x.AddProducer<CadastrarSolicitacaoSinapseEvent>(queueSettings.Topics.CadastroSolicitacaoSinapse);
            x.AddProducer<KafkaHealthCheckTopico>(queueSettings.Topics.Health);
        };
    }

    /// <summary>
    /// Configura os endpoints e opções adicionais do barramento Kafka.
    /// </summary>
    /// <returns>Ação que configura o <see cref="IKafkaFactoryConfigurator"/> com base nas configurações de fila.</returns>
    /// <exception cref="InvalidOperationException">Lançada quando as configurações de filas Kafka não são encontradas.</exception>
    public static Action<IRiderRegistrationContext, IKafkaFactoryConfigurator> ConfigureBus()
    {
        return (context, k) =>
        {
            IConfiguration configuration = context.GetRequiredService<IConfiguration>();
            KafkaQueueSettings queueSettings = configuration.GetSection(nameof(KafkaQueueSettings))
                .Get<KafkaQueueSettings>()
                ?? throw new InvalidOperationException("As configurações das filas Kafka estão ausentes.");

            k.TopicEndpoint<CadastrarSolicitacaoEvent>(queueSettings.Topics.CadastroSolicitacaoPI, queueSettings.GroupId, e =>
            {
                e.UseConsumeFilter(typeof(KafkaConsumeTracingFilter<>), context);

                e.ConfigureConsumer<CadastrarSolicitacaoEventHandler>(context, cfg =>
                {
                    cfg.UseMessageRetry(r => r.None());
                });
                e.CreateIfMissing();
                e.ConcurrentMessageLimit = 1;
                e.PrefetchCount = 1;
            });
        };
    }

    private static string SanitizarCertificado(string cert)
    {
        return cert.Replace("\\n", "\n", StringComparison.OrdinalIgnoreCase);
    }
}
