using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Sage.Shared.Diagnostics.Kafka;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using StackExchange.Redis;

namespace ONS.SINapse.Integracao.Sage.CrossCutting;

public static class HealthCheckExtension
{
    private const string UserAgent =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

    public static IServiceCollection AddSinapseHealthCheck(this IServiceCollection services,
        IConfiguration configuration)
    {
        ApplicationSettings? applicationSettings = configuration.GetSection(nameof(ApplicationSettings))
            .Get<ApplicationSettings>();
        ArgumentNullException.ThrowIfNull(applicationSettings);

        services.AddHealthChecks()
            .AddRedis(provider => provider.GetRequiredService<IConnectionMultiplexer>(),
                "Redis",
                tags: ["redis", "cache"], timeout: TimeSpan.FromSeconds(15))
            .AddKafka();

        services.AddHealthChecksUI(settings =>
            {
                if (!string.IsNullOrWhiteSpace(applicationSettings.SinapseIntegracaoSageHealthCheckUrl))
                {
                    settings.AddHealthCheckEndpoint("API SINapse Integracao Sage",
                        applicationSettings.SinapseIntegracaoSageHealthCheckUrl);
                }

                if (!string.IsNullOrWhiteSpace(applicationSettings.SinapseDadosHealthCheckUrl))
                {
                    settings.AddHealthCheckEndpoint("API SINapse Dados",
                        applicationSettings.SinapseDadosHealthCheckUrl);
                }

                if (!string.IsNullOrWhiteSpace(applicationSettings.SinapseHealthCheckUrl))
                {
                    settings.AddHealthCheckEndpoint("API SINapse", applicationSettings.SinapseHealthCheckUrl);
                }

                if (!string.IsNullOrWhiteSpace(applicationSettings.SinapseIntegracaoHealthCheckUrl))
                {
                    settings.AddHealthCheckEndpoint("API Integração SINapse",
                        applicationSettings.SinapseIntegracaoHealthCheckUrl);
                }

                settings.SetEvaluationTimeInSeconds(60);
                settings.UseApiEndpointHttpMessageHandler(_ => new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (_, cert, _, _) => cert != null,
                    UseDefaultCredentials = false,
                    AllowAutoRedirect = true,
                    PreAuthenticate = true
                });
                settings.ConfigureApiEndpointHttpclient((_, client) =>
                {
                    client.DefaultRequestHeaders.UserAgent.ParseAdd(UserAgent);
                });
            })
            .AddInMemoryStorage();

        return services;
    }

    public static void UseSinapseHealthCheck(this WebApplication app)
    {
        app.MapHealthChecks("/health", new HealthCheckOptions()
        {
            Predicate = _ => true,
            ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
        }).WithMetadata(new AllowAnonymousAttribute());

        app.UseHealthChecksUI(opts =>
        {
            opts.UIPath = "/monitor";
            opts.AddCustomStylesheet(Path.Combine(AppContext.BaseDirectory, "Resources", "monitor-ui.css"));
        });
    }
}
