using System.Globalization;
using System.IO.Compression;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.Business.Business.Validators;
using ONS.SINapse.Integracao.Sage.ExternalServices.Configurations;
using ONS.SINapse.Integracao.Sage.Shared;

namespace ONS.SINapse.Integracao.Sage.CrossCutting;

public static class DependencyInjector
{
    public static void Register(IServiceCollection services, IConfiguration configuration)
    {
        services
            .RegisterServices()
            .RegisterSharedLayer(configuration)
            .AddSinapseHealthCheck(configuration)
            .ConfigureLocalizationOptions()
            .ConfigureCompression()
            .RegisterContextAccessor()
            .RegisterCredencials(configuration)
            .AddSinapseDados(configuration)
            .RegisterValidators();
    }

    private static IServiceCollection RegisterServices(this IServiceCollection services)
    {
        services.AddHttpClient();
        return services;
    }

    private static IServiceCollection RegisterCredencials(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddSinapseAuthorization(configuration);

        return services;
    }

    private static IServiceCollection RegisterContextAccessor(this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddSingleton<IActionContextAccessor, ActionContextAccessor>();

        return services;
    }

    private static IServiceCollection ConfigureLocalizationOptions(this IServiceCollection services)
    {
        services.Configure<RequestLocalizationOptions>(options =>
        {
            var supportedCultures = new List<CultureInfo>
            {
                new("pt-BR")
            };

            options.DefaultRequestCulture = new RequestCulture("pt-BR");
            options.SupportedCultures = supportedCultures;
            options.SupportedUICultures = supportedCultures;
        });

        return services;
    }

    private static IServiceCollection ConfigureCompression(this IServiceCollection services)
    {
        services.Configure<GzipCompressionProviderOptions>(options => options.Level = CompressionLevel.Optimal);
        services.AddResponseCompression(options =>
        {
            options.Providers.Add<GzipCompressionProvider>();
        });

        return services;
    }

    private static IServiceCollection RegisterValidators(this IServiceCollection services)
    {
        services.AddScoped<IValidator<CadastrarSolicitacaoEvent>, CadastrarSolicitacaoEventValidator>();

        return services;
    }
}
