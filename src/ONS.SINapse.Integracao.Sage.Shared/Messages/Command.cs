using System.Text.Json.Serialization;
using FluentValidation.Results;
using MassTransit.Mediator;

namespace ONS.SINapse.Integracao.Sage.Shared.Messages;
public abstract class Command : Message, Request<ValidationResult>
{
    [JsonIgnore]
    public DateTime Timestamp { get; private set; }

    [JsonIgnore]
    public ValidationResult ValidationResult { get; protected set; }

    protected Command()
    {
        Timestamp = DateTime.UtcNow;
        ValidationResult = new ValidationResult();
    }

    public virtual Task<bool> EstaValidoAsync(CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}

public abstract class Command<TResult> : Message, Request<TResult> where TResult : CommandResult
{
    [JsonIgnore]
    public DateTime Timestamp { get; private set; }

    [JsonIgnore]
    public ValidationResult ValidationResult { get; protected set; }

    protected Command()
    {
        Timestamp = DateTime.UtcNow;
        ValidationResult = new ValidationResult();
    }

    public virtual Task<bool> EstaValidoAsync(CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}

public abstract class CommandResult
{
    protected CommandResult()
    {
        ValidationResult = new ValidationResult();
    }

    [JsonIgnore]
    public ValidationResult ValidationResult { get; private set; }

    public virtual void AdicionarValidation(ValidationResult validationResult)
        => ValidationResult = validationResult;
}
