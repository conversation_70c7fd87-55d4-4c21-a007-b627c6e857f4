namespace ONS.SINapse.Integracao.Sage.Shared.Settings;

public class ApiOptions
{
    public ApiOptions()
    {
        CustomHeaders = [];
        ServiceUri = string.Empty;
        Authentication = new AuthenticationSettings(string.Empty, 0, string.Empty, string.Empty);
    }

    public Dictionary<string, string>? CustomHeaders { get; set; }
    public string ServiceUri { get; set; }
    public AuthenticationSettings Authentication { get; set; }
}
