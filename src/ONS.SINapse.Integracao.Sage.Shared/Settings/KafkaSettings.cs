namespace ONS.SINapse.Integracao.Sage.Shared.Settings;
public class KafkaSettings
{
    public required bool RequireAuth { get; init; }
    public required string BootstrapServers { get; init; }
    public required string SecurityProtocol { get; init; }
    public required string SslCaPem { get; init; }
    public required string SslKeystorePassword { get; init; }
    public required string SaslMechanism { get; init; }
    public required string SaslUsername { get; init; }
    public required string SaslPassword { get; init; }
}
