using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using Refit;

namespace ONS.SINapse.Integracao.Sage.Shared.Services;

public interface IAuthenticationProvider<out TOptions> where TOptions : ApiOptions
{
    TOptions ObterApiOptions();
    Task<PopAutorizacaoTokenDto> ObterToken();
}

internal sealed class AuthenticationProvider<TOptions> : IAuthenticationProvider<TOptions> where TOptions : ApiOptions
{
    private readonly IAuthenticationService<TOptions> _authenticationService;
    private readonly TOptions _apiOptions;
    private readonly ConfiguracoesPopAuthSettings _popAuthSettings;

    public AuthenticationProvider(IAuthenticationService<TOptions> authenticationService, TOptions apiOptions, IOptions<ConfiguracoesPopAuthSettings> popAuthSettings)
    {
        _authenticationService = authenticationService;
        _apiOptions = apiOptions ?? throw new ArgumentNullException(nameof(apiOptions), "Erro de configuração de api.");
        _popAuthSettings = popAuthSettings.Value;
    }
    
    public TOptions ObterApiOptions() => _apiOptions;

    public Task<PopAutorizacaoTokenDto> ObterToken()
    {
        if (_apiOptions.Authentication is null)
        {
            throw new InvalidOperationException("Erro de configuração de autenticação da api.");
        }
        
        var form = new Dictionary<string, object>
        {
            { "client_id", _apiOptions.Authentication.ApplicationName },
            { "grant_type", "password" },
            { "username", _popAuthSettings.UserName },
            { "password", _popAuthSettings.Password }
        };
        
        return _authenticationService.ObterToken(form, _apiOptions.Authentication.ApplicationOrigin);
    }
}

internal sealed class AuthenticationCacheProvider<TOptions> : IAuthenticationProvider<TOptions> where TOptions : ApiOptions
{
    private readonly IAuthenticationProvider<TOptions> _inner;
    private readonly IDistributedCacheService _cacheService;

    public AuthenticationCacheProvider(IAuthenticationProvider<TOptions> inner, IDistributedCacheService cacheService)
    {
        _inner = inner;
        _cacheService = cacheService;
    }

    public TOptions ObterApiOptions() => _inner.ObterApiOptions();
    
    public async Task<PopAutorizacaoTokenDto> ObterToken()
    {
        TOptions apiOptions = _inner.ObterApiOptions();
        string keyCache = $"token: {apiOptions.Authentication.ApplicationName}";

        PopAutorizacaoTokenDto? token = await _cacheService.GetAsync<PopAutorizacaoTokenDto>(keyCache);

        if (token is not null)
        {
            return token!;
        }

        token = await _inner.ObterToken();
        
        await _cacheService.SetAsync(keyCache, token, TimeSpan.FromSeconds(apiOptions.Authentication.ApplicationCacheExpiration));

        return token!;
    }
}

//TOptions deve ser mantido para identificar qual service pertence a qual serviço de autenticação (AuthenticationProvider).
internal interface IAuthenticationService<TOptions> where TOptions : ApiOptions
{
    [Post("")]
    Task<PopAutorizacaoTokenDto> ObterToken([Body(BodySerializationMethod.UrlEncoded)] Dictionary<string, object> form, [Header("Origin")] string header);
    public Type GetTypeIntegration() => typeof(TOptions);
}
