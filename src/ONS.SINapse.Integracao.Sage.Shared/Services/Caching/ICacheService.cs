namespace ONS.SINapse.Integracao.Sage.Shared.Services.Caching;

public interface ICacheService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;
    Task SetAsync<T>(string key, T value, CancellationToken cancellationToken = default) where T : class;
    Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default) where T : class;
    Task SetAsync<T>(string key, T value, DateTimeOffset expiration, CancellationToken cancellationToken = default) where T : class;
    Task RemoveByPrefixAsync(string prefixKey, CancellationToken cancellationToken = default);
}
