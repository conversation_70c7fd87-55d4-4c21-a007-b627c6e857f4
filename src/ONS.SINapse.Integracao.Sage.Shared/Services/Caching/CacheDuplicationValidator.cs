using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Sage.Shared.Settings;

namespace ONS.SINapse.Integracao.Sage.Shared.Services.Caching;

public class CacheDuplicationValidator : ICacheDuplicationValidator
{
    private readonly IDistributedCacheService _distributedCacheService;
    private readonly RedisCacheSettings _redisCacheSettings;
    private readonly string _baseCacheKey = "SINAPSE_SAGE:";

    public CacheDuplicationValidator (IDistributedCacheService distributedCacheService, IOptions<RedisCacheSettings> redisCacheSettings)
    {
        _distributedCacheService = distributedCacheService;
        _redisCacheSettings = redisCacheSettings.Value;
    }

    public async Task<bool> ValidateAndSetAsync<T>(string key, string value)
    {
        string redisKey = _baseCacheKey + key;
        string? exist = await _distributedCacheService.GetAsync<string>(redisKey);

        if (exist is not null && exist == value)
        {
            return false;
        }

        await _distributedCacheService.SetAsync(redisKey, value, _redisCacheSettings.CacheExpiration);
        return true;
    }
}
