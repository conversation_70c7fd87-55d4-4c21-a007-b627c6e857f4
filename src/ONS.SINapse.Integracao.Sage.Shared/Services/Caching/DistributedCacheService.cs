using Newtonsoft.Json;
using StackExchange.Redis;

namespace ONS.SINapse.Integracao.Sage.Shared.Services.Caching;

public class DistributedCacheService : IDistributedCacheService
{
    private readonly IDatabase _redis;

    public DistributedCacheService(IDatabase redis)
    {
        _redis = redis;
    }

    public async Task<T?> GetAsync<T>(string key) 
        where T : class
    {
        string? cachedValue = await _redis.StringGetAsync(key);

        if (cachedValue is null)
        {
            return null;
        }

        T? value = JsonConvert.DeserializeObject<T>(cachedValue!);

        return value;
    }

    public Task SetAsync<T>(string key, T value, int seconds)
        where T : class =>
        SetValueAsync(key, value, TimeSpan.FromSeconds(seconds));

    public Task SetAsync<T>(string key, T value, TimeSpan expiration)
        where T : class =>
        SetValueAsync(key, value, expiration);

    private async Task SetValueAsync<T>(string key, T value, TimeSpan? expiration = null)
        where T : class
    {
        string cachedValue = JsonConvert.SerializeObject(value);

        await _redis.StringSetAsync(key, cachedValue, expiration);
    }
}
