using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace ONS.SINapse.Integracao.Sage.Shared.Diagnostics.Kafka;
public class KafkaHealthCheck : IHealthCheck
{
    private readonly IServiceProvider _serviceProvider;

    public KafkaHealthCheck(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            using IServiceScope scope = _serviceProvider.CreateScope();
            ITopicProducer<KafkaHealthCheckTopico> producer = scope.ServiceProvider.GetRequiredService<ITopicProducer<KafkaHealthCheckTopico>>();

            await producer.Produce(new KafkaHealthCheckTopico(), cancellationToken);

            return HealthCheckResult.Healthy("Kafka connection is healthy.");

        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy($"Kafka connection is unhealthy. Error: {ex.Message}", exception: ex);
        }
    }
}
