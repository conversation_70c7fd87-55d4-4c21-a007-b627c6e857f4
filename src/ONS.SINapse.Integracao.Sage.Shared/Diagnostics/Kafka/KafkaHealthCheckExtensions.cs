using Microsoft.Extensions.DependencyInjection;

namespace ONS.SINapse.Integracao.Sage.Shared.Diagnostics.Kafka;
public static class KafkaHealthCheckExtensions
{
    private const int TimeoutInSeconds = 10;
    private static readonly string[] tags = ["kafka"];

    public static IHealthChecksBuilder AddKafka(this IHealthChecksBuilder builder)
    {
        builder.Services.AddSingleton<KafkaHealthCheck>();

        builder.AddCheck<KafkaHealthCheck>("Kafka", 
            tags: tags,
            timeout: TimeSpan.FromSeconds(TimeoutInSeconds));

        return builder;
    }
}
