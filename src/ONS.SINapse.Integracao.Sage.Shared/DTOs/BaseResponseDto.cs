using Newtonsoft.Json;

namespace ONS.SINapse.Integracao.Sage.Shared.DTOs;

public class BaseResponseDto<TData> where TData : class
{       
    public BaseResponseDto(TData data, bool success, int statusCode, IEnumerable<string>? messages = null)
    {
        Success = success;
        Messages = messages ?? [];
        Data = data;
        StatusCode = statusCode;
    }

    [JsonProperty("success")]
    public bool Success { get; set; }
    [JsonProperty("statusCode")]
    public int StatusCode { get; set; }
    [JsonProperty("data")]
    public TData Data { get; set; }
    
    [JsonProperty("messages")]
    public IEnumerable<string> Messages { get; set; }
}
