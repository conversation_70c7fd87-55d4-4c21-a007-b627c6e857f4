using System.Globalization;
using NanoidDotNet;

namespace ONS.SINapse.Integracao.Sage.Shared.Factories;

public static partial class IdSolicitacaoFactory
{
    private const int RandomIdLength = 6;
    private const string DateFormat = "yyyyMMddHHmmss";
    private const string RandomAlphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    
    public static string GerarNovoId(string origem, string destino)
    {
        string datetime = DateTime.UtcNow.ToString(DateFormat, CultureInfo.InvariantCulture);
        string aleatorio = GenerateRandomString(RandomIdLength);

        string id = $"{datetime}-{origem.ToUpper()}-{aleatorio}-{destino.ToUpper()}";

        return id;
    }
    
    private static string GenerateRandomString(int length)
    {
        return Nanoid.Generate(RandomAlphabet, length);
    }
}
