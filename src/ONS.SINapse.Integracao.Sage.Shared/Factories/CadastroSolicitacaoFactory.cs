using ONS.SINapse.Integracao.Sage.Shared.DTOs;

namespace ONS.SINapse.Integracao.Sage.Shared.Factories;

public static class CadastroSolicitacaoFactory
{
    public static CadastroSolicitacaoDto FromDatasetItem(DatasetItemDto item, MensagemSolicitacaoDto mensagemSolicitacao)
    {
        return new CadastroSolicitacaoDto
        {
            Id = IdSolicitacaoFactory.GerarNovoId(item.Origin.Code, item.Destination.Code),
            Origem = ConvertManeuverToObjetoDeManobra(item.Origin),
            Destino = ConvertManeuverToObjetoDeManobra(item.Destination),
            Local = ConvertManeuverToObjetoDeManobra(item.Local),
            SistemaDeOrigem = "SAGE",
            Mensagem = GerarMensagem(item, mensagemSolicitacao.Acao),
            Tags = ["tensão_equipamento"],
            Usuario = GerarUsuario(mensagemSolicitacao.Usuario)
        };
    }

    private static ObjetoDeManobraDto ConvertManeuverToObjetoDeManobra(ManeuverObjectDto maneuver) => 
        new(maneuver.Code, maneuver.Name);

    private static string GerarMensagem(DatasetItemDto item, string acao) => 
        $"{FormatarNome(acao)} {item.Local.Name}";

    private static UsuarioSinapseDto GerarUsuario(string usuario) => 
        new() { Login = usuario, Nome = FormatarNome(usuario), Sid = usuario };

    private static string FormatarNome(string nome)
    {
        return string.Join(" ",
            nome.Split('.', StringSplitOptions.RemoveEmptyEntries)
                .Select(parte => char.ToUpper(parte[0]) + parte[1..].ToLower()));
    }
}

