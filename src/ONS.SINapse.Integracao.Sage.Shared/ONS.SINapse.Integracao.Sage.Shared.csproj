<Project Sdk="Microsoft.NET.Sdk">
	<ItemGroup>
		<PackageReference Include="FluentValidation" />
		<PackageReference Include="LinqKit" />
		<PackageReference Include="MassTransit" />
		<PackageReference Include="MassTransit.Kafka" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.WsFederation" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" />
		<PackageReference Include="Nanoid" />
		<PackageReference Include="Newtonsoft.Json" />
		<PackageReference Include="ons.configit.entidade" />
		<PackageReference Include="OpenTelemetry" />
		<PackageReference Include="Refit" />
		<PackageReference Include="Refit.HttpClientFactory" />
		<PackageReference Include="Refit.Newtonsoft.Json" />
		<PackageReference Include="Scrutor" />
		<PackageReference Include="Simple.OData.Client" />
		<PackageReference Include="System.Text.RegularExpressions" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
		<!-- Health Checks   -->
		<PackageReference Include="AspNetCore.HealthChecks.UI" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Core" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" />
		<PackageReference Include="AspNetCore.HealthChecks.Redis" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="Resources\monitor-ui.css">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>
