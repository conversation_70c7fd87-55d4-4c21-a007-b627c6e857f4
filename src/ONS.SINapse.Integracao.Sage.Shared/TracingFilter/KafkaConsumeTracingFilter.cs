using System.Diagnostics;
using MassTransit;
using MassTransit.Logging;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;

namespace ONS.SINapse.Integracao.Sage.Shared.TracingFilter;

public class KafkaConsumeTracingFilter<T> : IFilter<ConsumeContext<T>> where T : class
{
    public async Task Send(ConsumeContext<T> context, IPipe<ConsumeContext<T>> next)
    {
        TextMapPropagator propagator = Propagators.DefaultTextMapPropagator;

        PropagationContext parentContext = propagator.Extract(default, context.Headers, ExtractHeaderValue);

        using var activitySource = new ActivitySource(DiagnosticHeaders.DefaultListenerName);
        using Activity activity = activitySource.StartActivity(DiagnosticHeaders.DefaultListenerName, ActivityKind.Consumer, parentContext.ActivityContext);
        Baggage.Current = parentContext.Baggage;

        await next.Send(context);
    }

    public void Probe(ProbeContext context)
    {
        context.CreateFilterScope("KafkaConsumeTracingFilter");
    }

    private static IEnumerable<string> ExtractHeaderValue(Headers headers, string key)
    {
        if (headers.TryGetHeader(key, out object? value) && value is string raw)
        {
            yield return raw;
        }
    }
}
