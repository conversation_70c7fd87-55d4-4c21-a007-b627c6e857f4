using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Sage.Shared.Mediator;
using ONS.SINapse.Integracao.Sage.Shared.Notifications;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using StackExchange.Redis;

namespace ONS.SINapse.Integracao.Sage.Shared;

public static class DependencyInjector
{
    public static IServiceCollection RegisterSharedLayer(this IServiceCollection services, IConfiguration configuration)
    {
        RegisterNotification(services);
        RegisterSettings(services, configuration);
        RegisterCaching(services);
        RegisterRedisConnection(services, configuration);
        RegisterMediator(services);
        return services;
    }

    private static void RegisterCaching(IServiceCollection services)
    {
        services.AddSingleton<IDistributedCacheService, DistributedCacheService>();
        services.AddSingleton<ICacheDuplicationValidator, CacheDuplicationValidator>();
    }

    private static void RegisterRedisConnection(IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IConnectionMultiplexer>(_ =>
        {
            var options = ConfigurationOptions.Parse(configuration.GetConnectionString("Redis") ?? string.Empty);
            options.ConnectRetry = 100;
            options.ConnectTimeout = 3000;
            options.AbortOnConnectFail = false;
            return ConnectionMultiplexer.Connect(options);
        });

        services.AddSingleton<IDatabase>(provider =>
        {
            IConnectionMultiplexer connection = provider.GetRequiredService<IConnectionMultiplexer>();
            return connection.GetDatabase();
        });
        services.AddSingleton<IServer>(provider =>
        {
            IConnectionMultiplexer connection = provider.GetRequiredService<IConnectionMultiplexer>();
            return connection.GetServer(connection.GetEndPoints().FirstOrDefault()
                                        ?? throw new InvalidOperationException("Redis connection not found."));
        });
    }

    private static void RegisterSettings(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ConfiguracoesPopAuthSettings>(configuration.GetSection("ONS:PopAuth"));
        services.Configure<AuthorizationSettings>(configuration.GetSection("ONS:Authorization"));
        services.Configure<ApplicationSettings>(configuration.GetSection(nameof(ApplicationSettings)));
        services.Configure<OpenTelemetrySettings>(configuration.GetSection(nameof(OpenTelemetrySettings)));
        services.Configure<KafkaSettings>(configuration.GetSection(nameof(KafkaSettings)));
        services.Configure<KafkaQueueSettings>(configuration.GetSection(nameof(KafkaQueueSettings)));
        services.Configure<ApiOptions>(configuration.GetSection(nameof(ApiOptions)));
        services.Configure<AuthenticationSettings>(configuration.GetSection(nameof(AuthenticationSettings)));
        services.Configure<RedisCacheSettings>(configuration.GetSection(nameof(RedisCacheSettings)));
    }

    private static void RegisterNotification(IServiceCollection services)
    {
        services.AddScoped<NotificationContext>();
    }

    private static void RegisterMediator(IServiceCollection services)
    {
        services.AddScoped<IMediatorHandler, MediatorHandler>();
    }
}
