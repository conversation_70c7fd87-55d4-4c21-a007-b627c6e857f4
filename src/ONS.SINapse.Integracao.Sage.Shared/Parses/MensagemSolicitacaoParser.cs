using System.Text.RegularExpressions;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;

namespace ONS.SINapse.Integracao.Sage.Shared.Parses;
public static partial class MensagemSolicitacaoParser
{
    public static MensagemSolicitacaoDto? Parse(string message)
    {
        if (string.IsNullOrWhiteSpace(message))
        {
            return null;
        }

        Regex regex = MessagePIRegex();
        Match match = regex.Match(message);
        if (!match.Success)
        {
            return null;
        }

        return new MensagemSolicitacaoDto
        {
            Acao = match.Groups[1].Value,
            Ido = match.Groups[2].Value,
            Usuario = match.Groups[3].Value
        };
    }

    [GeneratedRegex(@"^Nota de operação -> SINapse #(.*?)#(.*?)# pelo usuario #reger\+(.*?)#$")]
    private static partial Regex MessagePIRegex();
}
