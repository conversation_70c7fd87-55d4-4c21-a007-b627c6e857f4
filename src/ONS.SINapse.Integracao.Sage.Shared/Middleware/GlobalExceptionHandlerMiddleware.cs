using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;

namespace ONS.SINapse.Integracao.Sage.Shared.Middleware;

public class GlobalExceptionHandlerMiddleware : IMiddleware
{
    private readonly ILogger<GlobalExceptionHandlerMiddleware> _logger;

    public GlobalExceptionHandlerMiddleware(ILogger<GlobalExceptionHandlerMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "[EXCEPTION] {Message} (InnerException: {InnerException})",
                ex.Message, ex.InnerException);
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task<Task> HandleExceptionAsync(HttpContext context, Exception exception)
    {
        HttpStatusCode statusCode = HttpStatusCode.InternalServerError;

        IEnumerable<string> mensagens;

        if (exception is Refit.ApiException apiEx)
        {
            statusCode = apiEx.StatusCode;
            dynamic? conteudo = await apiEx.GetContentAsAsync<dynamic>();
            string detalhe = conteudo?.detail ?? apiEx.Message;

            mensagens = [detalhe];
        }
        else
        {
            mensagens = [exception.InnerException?.Message ?? exception.Message];
        }

        context.Response.ContentType = "application/json";
        context.Response.StatusCode = (int)statusCode;

        var response = new BaseResponseDto<object>(exception, false, (int)statusCode, mensagens);
        string json = JsonConvert.SerializeObject(response,
            new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            });

        return context.Response.WriteAsync(json);
    }
}

public class ExceptionInfo
{
    public ExceptionInfo(Exception exception)
    {
        Message = exception.Message;
        StackTrace = exception.StackTrace ?? string.Empty;
        Source = exception.Source ?? string.Empty;
        InnerException = exception.InnerException is not null 
            ? new ExceptionInfo(
                exception.InnerException.Message, 
                exception.InnerException.StackTrace ?? string.Empty, 
                exception.InnerException.Source ?? string.Empty
                ) 
            : null;
    }

    public ExceptionInfo(string message, string stackTrace, string source)
    {
        Message = message;
        StackTrace = stackTrace;
        Source = source;
    }
    
    public string Message { get; set; }
    public string StackTrace { get; set; }
    public string Source { get; set; }
    public ExceptionInfo? InnerException { get; set; }
}
