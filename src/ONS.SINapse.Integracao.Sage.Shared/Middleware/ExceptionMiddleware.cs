using System.Net;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace ONS.SINapse.Integracao.Sage.Shared.Middleware;

public class ExceptionMiddleware : IMiddleware
{
    public Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            return next(context);
        }
        catch (Exception ex)
        {
            var options = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                Formatting = Formatting.None,
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                PreserveReferencesHandling = PreserveReferencesHandling.None,
                ContractResolver = null
            };
            
            var result = new ExceptionInfo(ex); 

            string resultJson = JsonConvert.SerializeObject(result, options);

            context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
            context.Response.ContentType = "application/json";

            return context.Response.WriteAsync(resultJson);
        }
    }
}

public class ExceptionInfo
{
    public ExceptionInfo(Exception exception)
    {
        Message = exception.Message;
        StackTrace = exception.StackTrace ?? string.Empty;
        Source = exception.Source ?? string.Empty;
        InnerException = exception.InnerException is not null 
            ? new ExceptionInfo(
                exception.InnerException.Message, 
                exception.InnerException.StackTrace ?? string.Empty, 
                exception.InnerException.Source ?? string.Empty
                ) 
            : null;
    }

    public ExceptionInfo(string message, string stackTrace, string source)
    {
        Message = message;
        StackTrace = stackTrace;
        Source = source;
    }
    
    public string Message { get; set; }
    public string StackTrace { get; set; }
    public string Source { get; set; }
    public ExceptionInfo? InnerException { get; set; }
}
