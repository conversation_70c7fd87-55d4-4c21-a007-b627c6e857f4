using MassTransit;
using MassTransit.Mediator;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Sage.Shared.Extensions;
using ONS.SINapse.Integracao.Sage.Shared.Messages;
using ONS.SINapse.Integracao.Sage.Shared.Notifications;
using Event = ONS.SINapse.Integracao.Sage.Shared.Messages.Event;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ONS.SINapse.Integracao.Sage.Shared.Mediator;
public class MediatorHandler : IMediatorHandler
{
    private readonly IMediator _mediator;
    private readonly ILogger<MediatorHandler> _logger;
    private readonly NotificationContext _notificationContext;

    public MediatorHandler(IMediator mediator, ILogger<MediatorHandler> logger, NotificationContext notificationContext)
    {
        _mediator = mediator;
        _logger = logger;
        _notificationContext = notificationContext;
    }

    public async Task<ValidationResult> EnviarComandoAsync<T>(T comando, CancellationToken cancellationToken) where T : Command
    {
        _logger.LogInformation("Enviando Comando {MessageType}", comando.MessageType);

        ValidationResult result = await _mediator.SendRequest(comando, cancellationToken);

        if (!comando.ValidationResult.IsValid)
        {
            result.AdicionarErro(comando.ValidationResult);
        }

        _logger.LogInformation("Comando {MessageType} Enviado e finalizado Result Messages: {Result}", comando.MessageType,
            result
                .Errors
                .Select(failure => $"{failure.Severity} - {failure.ErrorMessage}"));

        if (!result.IsValid)
        {
            _notificationContext.AddNotifications(result);
        }

        return result;
    }

    public async Task<TResult> EnviarComandoAsync<TCommand, TResult>(TCommand comando, CancellationToken cancellationToken)
        where TCommand : Command<TResult>
        where TResult : CommandResult
    {
        _logger.LogInformation("Enviando Comando {MessageType}", comando.MessageType);

        TResult result = await _mediator.SendRequest(comando, cancellationToken);

        if (!comando.ValidationResult.IsValid)
        {
            result.ValidationResult.AdicionarErro(comando.ValidationResult);
        }

        _logger.LogInformation("Comando {MessageType} Enviado e finalizado Result Messages: {Result}", comando.MessageType,
            result
                .ValidationResult
                .Errors
                .Select(failure => $"{failure.Severity} - {failure.ErrorMessage}"));

        if (!result.ValidationResult.IsValid)
        {
            _notificationContext.AddNotifications(result.ValidationResult);
        }

        return result;
    }


    public async Task PublicarEventoAsync<T>(T evento, CancellationToken cancellationToken) where T : Event
    {
        _logger.LogInformation("Publicando Evento {MessageType}", evento.MessageType);
        await _mediator.Publish(evento, cancellationToken);

        _logger.LogInformation("Evento {MessageType} Publicado.", evento.MessageType);
    }
}
