using System.Diagnostics;
using System.Text;
using System.Text.Json;
using MassTransit;
using Microsoft.Extensions.Logging;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;

namespace ONS.SINapse.Integracao.Sage.Shared.Observer;

public class KafkaSendObserver : ISendObserver
{
    private readonly ILogger<KafkaSendObserver> _logger;
    private static readonly TextMapPropagator Propagator = Propagators.DefaultTextMapPropagator;

    public KafkaSendObserver(ILogger<KafkaSendObserver> logger) => _logger = logger;

    public Task PreSend<T>(SendContext<T> context) where T : class
    {
        var propagationContext = new PropagationContext(
            Activity.Current?.Context ?? default,
            Baggage.Current
        );

        Propagator.Inject(
            propagationContext,
            context.Headers,
            (headers, key, value) => headers.Set(key, Encoding.UTF8.GetBytes(value))
        );

        return Task.CompletedTask;
    }

    public Task PostSend<T>(SendContext<T> context) where T : class =>  Task.CompletedTask;

    public Task SendFault<T>(SendContext<T> context, Exception exception) where T : class
    {
        _logger.LogError(
            "Erro ao enviar mensagem para Kafka: {MessageType} | Exceção: {ExceptionMessage} | Mensagem: {MessageBody}",
            typeof(T).Name,
            exception.Message,
            JsonSerializer.Serialize(context.Message)
        );
        
        Activity.Current?.SetStatus(ActivityStatusCode.Error);
        Activity.Current?.SetTag("exception.message", exception.Message);
        Activity.Current?.SetTag("exception.stacktrace", exception.StackTrace);

        return Task.CompletedTask;
    }
}

