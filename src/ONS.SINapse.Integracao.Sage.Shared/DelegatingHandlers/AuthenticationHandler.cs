using System.Net.Http.Headers;
using ONS.SINapse.Integracao.Sage.Shared.Services;
using ONS.SINapse.Integracao.Sage.Shared.Settings;

namespace ONS.SINapse.Integracao.Sage.Shared.DelegatingHandlers;


public class AuthenticationHandler<TApiOptions> : DelegatingHandler where TApiOptions : ApiOptions
{
    private readonly IAuthenticationProvider<TApiOptions> _provider;
    
    public AuthenticationHandler(IAuthenticationProvider<TApiOptions> provider) => _provider = provider;

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        DTOs.PopAutorizacaoTokenDto token = await _provider.ObterToken();
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);
        return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
    }
}
