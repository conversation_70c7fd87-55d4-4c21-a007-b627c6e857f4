using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Sage.Shared.DelegatingHandlers;
using ONS.SINapse.Integracao.Sage.Shared.Settings;

namespace ONS.SINapse.Integracao.Sage.Shared.Extensions;
public static class HttpClientBuilderExtensions
{
    public static IHttpClientBuilder AddAuthenticationRequestHandler<TApiOptions>(this IHttpClientBuilder builder, IConfiguration configuration) where TApiOptions : ApiOptions
    {
        builder.Services.AddApiAuthentication<TApiOptions>(configuration);
        builder.AddHttpMessageHandler<AuthenticationHandler<TApiOptions>>();
        return builder;
    }
}
