using System.Collections;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Identity;

namespace ONS.SINapse.Integracao.Sage.Shared.Extensions;

public static class UserContextExtensions
{
    public static string NomeUsuario(this IUserContext userContext)
    {
        string? saida = userContext.ObterValorUserContext<string>("userName");
        return saida ?? string.Empty;
    }

    public static string Nome(this IUserContext userContext)
    {
        string? saida = userContext.ObterValorUserContext<string>("givenName");
        return saida ?? string.Empty;
    }

    public static string Email(this IUserContext userContext)
    {
        string saida = userContext.ObterValorUserContext<string>("email");

        return saida;
    }

    public static string IdUsuario(this IUserContext userContext)
    {
        string? saida = userContext.ObterValorUserContext<string>("userId");

        return saida ?? string.Empty;
    }

    public static List<string> Perfis(this IUserContext userContext)
    {
        List<string> saida = userContext.ObterValorUserContext<List<string>>("roles");

        return saida;
    }

    public static List<string> Escopos(this IUserContext userContext)
    {
        List<string> saida = userContext.ObterValorUserContext<List<string>>("scopes");

        return saida;
    }

    public static List<string> EscoposOperacoes(this IUserContext userContext)
    {
        List<string> saida = userContext.ObterValorUserContext<List<string>>("scopeOperations");

        return saida;
    }

    public static List<string> Operacoes(this IUserContext userContext)
    {
        List<string> saida = userContext.ObterValorUserContext<List<string>>("operations");

        return saida;
    }

    public static UsuarioDto Usuario(this IUserContext userContext)
    {
        return new UsuarioDto
        {
            Login = NomeUsuario(userContext),
            Nome = Nome(userContext),
            Sid = IdUsuario(userContext),
            Escopos = Escopos(userContext),
            Operacoes = Operacoes(userContext),
            Perfis = Perfis(userContext),
            Perfil = Perfis(userContext)[0],
            EscopoOperacoes = EscoposOperacoes(userContext)
        };
    }

    public static void AdicionarAgentes(this IUserContext userContext, List<string> escopo)
    {
        const string usinasKey = "agentes";
        var agentesEscopo = escopo.Where(x => x.Contains("AGENTES/")).ToList();
        if (agentesEscopo.Count != 0)
        {
            var usinas = agentesEscopo.Select(x => x.Replace("AGENTES/", "")).ToList();
            userContext.AdicionarDados(usinasKey, usinas);
        }
    }

    public static void AdicionarDados<TValue>(this IUserContext userContext, string key, TValue data)
    {
        userContext.AdditionalData ??= [];
        userContext.CriarChaveCasoNaoExista<TValue>(key);
        userContext.AdditionalData[key] = data;
    }

    private static void CriarChaveCasoNaoExista<TValue>(this IUserContext userContext, string key)
    {
        if (!userContext.AdditionalData.ContainsKey(key))
        {
            userContext.AdditionalData.Add(key, default(TValue));
        }
    }

    private static TResult ObterValorUserContext<TResult>(this IUserContext userContext, string key)
    {
        TResult? saida = default;

        if (!(userContext?.AdditionalData?.ContainsKey(key) ?? false))
        {
            return saida!;
        }

        try
        {
            saida = (TResult)userContext.AdditionalData[key]!;
        }
        catch
        {
            // ignored
        }

        return saida!;

    }
}
