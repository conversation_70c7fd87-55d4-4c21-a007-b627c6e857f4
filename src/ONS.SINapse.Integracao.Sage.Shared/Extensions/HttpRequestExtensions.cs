using Microsoft.AspNetCore.Http;

namespace ONS.SINapse.Integracao.Sage.Shared.Extensions;

public static class HttpRequestExtensions
{
    public static string? GetTokenFromAuthorizationHeaderOrQuery(this HttpRequest request)
    {
        // Tenta pegar do Header Authorization
        string? authorization = request.Headers.Authorization.FirstOrDefault();
        if (!string.IsNullOrEmpty(authorization) && authorization.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
        {
            return authorization["Bearer ".Length..].Trim();
        }

        // Se não encontrou, tenta pegar da 'Query' 'String' (ex: para WebSocket)
        string? accessToken = request.Query["access_token"].FirstOrDefault();
        return !string.IsNullOrEmpty(accessToken) ? accessToken : null;
    }
}

