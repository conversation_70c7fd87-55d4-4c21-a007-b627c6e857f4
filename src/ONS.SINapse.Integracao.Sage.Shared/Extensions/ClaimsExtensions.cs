using System.Security.Claims;
using ONS.SINapse.Integracao.Sage.Shared.Identity;

namespace ONS.SINapse.Integracao.Sage.Shared.Extensions;

public static class ClaimsExtensions
{
    public static List<string> GetValuesOfType(this IEnumerable<Claim> claims, string type)
    {
        return claims.Where(x => x.Type == type).Select(x => x.Value).Distinct().ToList();
    }

    public static string ObterSidDeUsuario(this ClaimsPrincipal userClaims)
        => userClaims.FindFirst(PopClaimTypes.Sid)?.Value ?? "";

}
