using System.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.Integracao.Sage.Shared.DelegatingHandlers;
using ONS.SINapse.Integracao.Sage.Shared.Services;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using Refit;

namespace ONS.SINapse.Integracao.Sage.Shared.Extensions;

public static class AuthenticationProviderExtensions
{
    public static IServiceCollection AddApiAuthentication<TOptions>(this IServiceCollection services, IConfiguration configuration) where TOptions : ApiOptions
    {
        IConfigurationSection section = configuration.GetSection(typeof(TOptions).Name);
        TOptions? apiOptions = section.Get<TOptions>();
        ArgumentNullException.ThrowIfNull(apiOptions);

        services.Configure<TOptions>(section);

        services.AddApiAuthenticationProvider(apiOptions);
        
        return services;
    }
    
    private static void AddApiAuthenticationProvider<TOptions>(this IServiceCollection services, TOptions apiOptions) where TOptions : ApiOptions
    {
        if (apiOptions == null)
        {
            throw new ArgumentNullException(nameof(apiOptions), "Erro de configuração de api.");
        }

        if (apiOptions.Authentication == null)
        {
            throw new ArgumentNullException(nameof(apiOptions), "Erro de configuração de autenticação da api.");
        }

        services.TryAddScoped(_ => apiOptions);

        services.AddScoped<IAuthenticationProvider<TOptions>, AuthenticationProvider<TOptions>>()
            .Decorate<IAuthenticationProvider<TOptions>, AuthenticationCacheProvider<TOptions>>();

        services.TryAddTransient<AuthenticationHandler<TOptions>>();
        
        var refitOptions = new RefitSettings(new NewtonsoftJsonContentSerializer(new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        }))
        {
            CollectionFormat = CollectionFormat.Multi
        };
        
        services
            .AddRefitClient<IAuthenticationService<TOptions>>(refitOptions)
            .ConfigureHttpClient(options =>
            {
                options.BaseAddress = new Uri(apiOptions.Authentication.ServiceUri);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (_, cert, _, _) => cert != null,
                UseDefaultCredentials = true,
                AllowAutoRedirect = true,
                PreAuthenticate = true,
                Credentials = CredentialCache.DefaultNetworkCredentials,
                DefaultProxyCredentials = CredentialCache.DefaultNetworkCredentials
            });
    }
}
