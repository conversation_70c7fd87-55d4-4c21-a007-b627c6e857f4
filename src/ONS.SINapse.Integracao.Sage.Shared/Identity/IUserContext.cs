using System.Collections;
using System.Net;
using FluentValidation.Results;
using Microsoft.AspNetCore.Http;
using ONS.SINapse.Integracao.Sage.Shared.Extensions;

namespace ONS.SINapse.Integracao.Sage.Shared.Identity;

public interface IUserContext
{
    DateTime StartDateTime { get; set; }

    Guid RequestId { get; set; }

    Hashtable AdditionalData { get; set; }

    ValidationResult ValidationResult { get; set; }

    Hashtable UnhandledExceptions { get; set; }
}

public class UserContext : IUserContext
{
    public UserContext(IHttpContextAccessor httpContextAccessor)
    {
        DefinirDadosDoUsuario(httpContextAccessor);

        ValidationResult = new ValidationResult();
        AdditionalData = [];
    }

    public DateTime StartDateTime { get; set; }

    public Guid RequestId { get; set; }

    public Hashtable AdditionalData { get; set; }

    public ValidationResult ValidationResult { get; set; }

    public Hashtable UnhandledExceptions { get; set; } = [];


    private void DefinirDadosDoUsuario(IHttpContextAccessor httpContextAccessor)
    {
        if (httpContextAccessor.HttpContext is null)
        {
            return;
        }

        HttpContext? httpContext = httpContextAccessor.HttpContext;

        if (!(httpContext.User.Identity?.IsAuthenticated ?? false))
        {
            return;
        }

        string? userName = httpContext.User.FindFirst(PopClaimTypes.NameIdentifier)?.Value;
        string? userId = httpContext.User.FindFirst(PopClaimTypes.Sid)?.Value;
        string? givenName = httpContext.User.FindFirst(PopClaimTypes.GivenName)?.Value;
        string? email = httpContext.User.FindFirst(PopClaimTypes.Upn)?.Value;
        List<string> scopes = httpContext.User.Claims.GetValuesOfType(PopClaimTypes.Scope);
        List<string> roles = httpContext.User.Claims.GetValuesOfType(PopClaimTypes.Role);
        List<string> scopeOperations = httpContext.User.Claims.GetValuesOfType(PopClaimTypes.ScopeOperation);
        List<string> operations = httpContext.User.Claims.GetValuesOfType(PopClaimTypes.Operation);

        this.AdicionarDados("roles", roles);
        this.AdicionarDados("userName", userName);
        this.AdicionarDados("userId", userId);
        this.AdicionarDados("givenName", givenName);
        this.AdicionarDados("email", email);
        this.AdicionarDados("scopes", scopes);
        this.AdicionarDados("scopeOperations", scopeOperations);
        this.AdicionarDados("operations", operations);
        this.AdicionarAgentes(scopes);
    }
}

public interface ISourceInfo
{
    Hashtable Data { get; set; }

    IPAddress IP { get; set; }
}
