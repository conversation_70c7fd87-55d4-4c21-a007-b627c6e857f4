using System.Security.Claims;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace ONS.SINapse.Integracao.Sage.Shared.Identity.Claims;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public sealed class ClaimRequirementAttribute : TypeFilterAttribute
{
    public ClaimRequirementAttribute(string claimType, string claimValue)
        : base(typeof(ClaimRequirementFilter))
    {
        Arguments = [new Claim(claimType, claimValue)];
    }
}

public class ClaimRequirementFilter(Claim claim) : IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        bool hasClaim = context.HttpContext.User
            .Claims.Any(c => c.Type == claim.Type && claim.Value.Split(",").Contains(c.Value));
        if (!hasClaim)
        {
            context.Result = new ForbidResult();
        }
    }
}
