using Microsoft.AspNetCore.Authorization;

namespace ONS.SINapse.Integracao.Sage.Shared.Identity;
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public sealed class ClaimAuthorizeAttribute :
    AuthorizeAttribute,
    IAuthorizationRequirement,
    IClaimAuthorizeAttribute
{
    public string ClaimType { get; set; }

    public string[] ClaimValues { get; set; }

    public ClaimAuthorizeAttribute(string type, string value)
    {
        ClaimType = type;
        ClaimValues = value.Split(',');
    }
}
