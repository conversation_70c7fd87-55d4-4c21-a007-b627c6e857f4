#nullable disable

using System.Security.Claims;

namespace ONS.SINapse.Integracao.Sage.Shared.Identity;

public static class PopClaimTypes
{
    public const string ScopeOperation = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/scopeoperation";
    public const string Operation = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/operation";
    public const string Scope = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/scope";
    public const string Role = "http://schemas.microsoft.com/ws/2008/06/identity/claims/role";
    public const string GivenName = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname";
    public const string NameIdentifier = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier";
    public const string Upn = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn";
    public const string Sid = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/sid";

    public static string ObterClaim(this ClaimsPrincipal usuario, string type)
    {
        return usuario.ObterClaim(type, (string)null);
    }

    private static string ObterClaim(this ClaimsPrincipal usuario, string type, string defaultvalue)
    {
        string str = defaultvalue;
        if (usuario != null && usuario.HasClaim((Predicate<Claim>)(c => c.Type == type)))
        {
            str = usuario.FindFirst(type)!.Value;
        }

        return str;
    }
}
