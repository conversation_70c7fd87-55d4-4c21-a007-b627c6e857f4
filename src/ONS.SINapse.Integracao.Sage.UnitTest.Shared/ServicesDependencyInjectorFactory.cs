using FluentValidation;
using MassTransit;
using MassTransit.Testing;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq.AutoMock;
using Newtonsoft.Json;
using ONS.SINapse.Integracao.Sage.Business;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.ExternalServices.Services;
using ONS.SINapse.Integracao.Sage.Shared.Mediator;
using ONS.SINapse.Integracao.Sage.Shared.Messages;
using ONS.SINapse.Integracao.Sage.Shared.Notifications;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared.Bus;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared.JsonConverters;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Shared;

public class ServicesDependencyInjectorFactory : IDisposable
{
    private readonly IServiceCollection _serviceCollection;
    private AutoMocker _mocker;

    public ServicesDependencyInjectorFactory(AutoMocker mocker)
    {
        _serviceCollection = new ServiceCollection();
        _mocker = mocker;
    }

    public void RegisterServices()
    {
        AddMassTransit();
        AddLogger();
        AddMediatorHandler();
        AddNotificationContext();
        AddHttpContextAccessor();
        AddOptions();
        AddKafka();
        AddCache();
        AddValidators();
        AddServices();
    }

    public void DefinirMocker(AutoMocker mocker)
    {
        _mocker = mocker;
    }

    public IServiceProvider BuildServiceProvider()
    {
        return _serviceCollection.BuildServiceProvider(true);
    }

    private void AddMassTransit()
    {
        _serviceCollection.AddMassTransitTestHarness(options =>
        {
            options.AddConsumers(typeof(CommandHandler<>).Assembly, typeof(BusinessApplicationReference).Assembly);
            options.AddRequestClient<Command>();
            options.SetDefaultRequestTimeout(TimeSpan.FromSeconds(60));
            options.SetTestTimeouts(TimeSpan.FromSeconds(60));

            options.UsingInMemory((context, cfg) =>
            {
                var jsonSettings = new JsonSerializerSettings
                {
                    ContractResolver = new IncludePrivateContractResolver(),
                    TypeNameHandling = TypeNameHandling.All,
                    ConstructorHandling = ConstructorHandling.AllowNonPublicDefaultConstructor,
                    ObjectCreationHandling = ObjectCreationHandling.Replace
                };

                jsonSettings.Converters.Add(new ReadOnlyCollectionConverter());

                cfg.ConfigureNewtonsoftJsonDeserializer(_ => jsonSettings);
                cfg.ConfigureNewtonsoftJsonSerializer(_ => jsonSettings);

                cfg.UseNewtonsoftJsonDeserializer();
                cfg.UseNewtonsoftJsonSerializer();
                cfg.UseNewtonsoftRawJsonDeserializer();
                cfg.UseNewtonsoftRawJsonSerializer();

                cfg.ConfigureEndpoints(context);
            });

            options.DisableUsageTelemetry();
        });
    }

    private void AddLogger()
    {
        _serviceCollection.AddLogging(builder =>
        {
            builder.AddConsole();
        });
    }

    private void AddMediatorHandler()
    {
        _serviceCollection.AddTransient<IMediatorHandler>(_ => _mocker.Get<IMediatorHandler>());
    }

    private void AddKafka()
    {
        _serviceCollection.AddScoped<IPublishEndpoint>(sp => sp.GetRequiredService<ITestHarness>().Bus);

        _serviceCollection.AddScoped<ITopicProducer<CadastrarSolicitacaoSinapseEvent>>(sp =>
            new TestKafkaTopicProducer<CadastrarSolicitacaoSinapseEvent>(sp.GetRequiredService<IPublishEndpoint>()));
    }

    private void AddNotificationContext()
    {
        _serviceCollection.AddTransient<NotificationContext>(_ => _mocker.Get<NotificationContext>());
    }

    private void AddHttpContextAccessor()
    {
        _serviceCollection.AddTransient<IHttpContextAccessor>(_ => _mocker.Get<IHttpContextAccessor>());
    }

    private void AddOptions()
    {
        _serviceCollection.AddTransient<IOptions<AuthorizationSettings>>(_ => _mocker.Get<IOptions<AuthorizationSettings>>());
    }

    private void AddCache()
    {
        _serviceCollection.AddTransient<IDistributedCacheService>(_ => _mocker.Get<IDistributedCacheService>());
    }

    private void AddValidators()
    {
        _serviceCollection.AddTransient<IValidator<CadastrarSolicitacaoEvent>>(_ => _mocker.Get<IValidator<CadastrarSolicitacaoEvent>>());
    }

    private void AddServices()
    {
        _serviceCollection.AddTransient<ICacheDuplicationValidator>(_ => _mocker.Get<ICacheDuplicationValidator>());
        _serviceCollection.AddTransient<ISinapseDadosDatasetService>(_ => _mocker.Get<ISinapseDadosDatasetService>());
    }

    public void Dispose()
    {
        // release managed resources here
    }
}
