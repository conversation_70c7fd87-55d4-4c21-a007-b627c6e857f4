using MassTransit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Shared.Bus;
public sealed class TestKafkaTopicProducer<T> : ITopicProducer<T> where T : class
{
    private readonly IPublishEndpoint _publishEndpoint;

    public TestKafkaTopicProducer(IPublishEndpoint publishEndpoint)
    {
        _publishEndpoint = publishEndpoint;
    }

    public ConnectHandle ConnectSendObserver(ISendObserver observer)
    {
        throw new NotImplementedException();
    }

    public Task Produce(T message, CancellationToken cancellationToken = default)
    {
        return _publishEndpoint.Publish(message, cancellationToken);
    }

    public Task Produce(T message, IPipe<KafkaSendContext<T>> pipe, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task Produce(object values, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task Produce(object values, IPipe<KafkaSendContext<T>> pipe, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}
