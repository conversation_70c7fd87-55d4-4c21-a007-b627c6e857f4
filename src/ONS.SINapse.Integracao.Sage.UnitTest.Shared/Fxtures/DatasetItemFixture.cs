using ONS.SINapse.Integracao.Sage.Shared.DTOs;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Shared.Fxtures;

public static class DatasetItemFixture
{
    public static DatasetItemDto ObterDatasetItemValido()
    {
        var origem = new ManeuverObjectDto("NE", "CORS-NE");
        var destino = new ManeuverObjectDto("GSU", "ENGIE");
        var local = new ManeuverObjectDto("Teste-Unitario", "Teste Unitário");

        return new DatasetItemDto()
        {
            Id = "Teste-Unitario",
            Origin = origem,
            Destination = destino,
            Description = "Teste Unitário",
            Label = "Teste Unitário",
            Local = local
        };
    }
}
