namespace ONS.SINapse.Integracao.Sage.UnitTest.Shared.Fxtures;

public static class CacheTestFixture
{
    public static TestCacheObject ObterObjetoTesteValido()
    {
        return new TestCacheObject
        {
            Id = "test-id-123",
            Nome = "Objeto de Teste",
            Valor = 42.5m,
            DataCriacao = new DateTime(2025, 6, 30, 10, 30, 0, DateTimeKind.Utc),
            Ativo = true,
            Tags = ["tag1", "tag2", "tag3"],
            Propriedades = new Dictionary<string, object>
            {
                { "prop1", "valor1" },
                { "prop2", 123 },
                { "prop3", true }
            }
        };
    }

    public static string ObterJsonObjetoTeste()
    {
        return """{"Id":"test-id-123","Nome":"Objeto de Teste","Valor":42.5,"DataCriacao":"2025-06-30T10:30:00","Ativo":true,"Tags":["tag1","tag2","tag3"],"Propriedades":{"prop1":"valor1","prop2":123,"prop3":true}}""";
    }
}

public class TestCacheObject
{
    public string Id { get; set; } = string.Empty;
    public string Nome { get; set; } = string.Empty;
    public decimal Valor { get; set; }
    public DateTime DataCriacao { get; set; }
    public bool Ativo { get; set; }
    public List<string> Tags { get; set; } = [];
    public Dictionary<string, object> Propriedades { get; set; } = [];
}
