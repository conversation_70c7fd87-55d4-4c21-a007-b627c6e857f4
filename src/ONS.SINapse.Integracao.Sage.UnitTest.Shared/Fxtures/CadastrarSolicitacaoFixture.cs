using ONS.SINapse.Integracao.Sage.Shared.DTOs;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Shared.Fxtures;
public static class CadastrarSolicitacaoFixture
{
    public static CadastroSolicitacaoDto ObterCadastroSolicitacaoValido(
            bool withoutOrigem = false,
            bool withoutDest<PERSON> = false,
            bool withoutMensagem = false
        )
    {
        var origem = new ObjetoDeManobraDto("NE", "CORS-NE");
        var destino = new ObjetoDeManobraDto("GSU", "ENGIE");

        return new CadastroSolicitacaoDto()
        {
            Id = "Id-Integração-Teste",
            Origem = withoutOrigem ? null : origem,
            Destino = withoutDestino ? null : destino,
            Local = new ObjetoDeManobraDto("SPBAR", "BARIRI"),
            EncaminharPara = new ObjetoDeManobraDto("GSU", "ENGIE"),
            SistemaDeOrigem = "SINapse_Integracao_Sage",
            InformacaoAdicional = "Corte de teste de unidade",
            Mensagem = withoutMensagem ? null : "Corte de teste de unidade",
            Motivo = "Teste",
            Usuario = new UsuarioSinapseDto() { Login = "<EMAIL>", Sid = "sid-usuario-teste", Nome = "Usuário Teste" }
        };
    }
}
