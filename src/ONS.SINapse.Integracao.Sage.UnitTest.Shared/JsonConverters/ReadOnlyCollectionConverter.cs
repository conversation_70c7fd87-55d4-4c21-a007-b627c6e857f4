using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Shared.JsonConverters;

public class ReadOnlyCollectionConverter : JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        // Se for IReadOnlyCollection<T>
        return objectType.IsGenericType &&
               objectType.GetGenericTypeDefinition() == typeof(IReadOnlyCollection<>);
    }

    public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        // Carrega o token inteiro (pode ser um objeto com metadados ou um array)
        var token = JToken.Load(reader);
    
        // Se for um objeto (com metadados, como $type e $values)
        if (token.Type == JTokenType.Object)
        {
            var obj = (JObject)token;
            // Remove o metadado $type, se existir
            obj.Remove("$type");
    
            // Se houver um $values, essa é a coleção real
            if (obj.TryGetValue("$values", out JToken? valuesToken))
            {
                token = valuesToken;
            }
        }

        // Obtém o tipo de elemento (por exemplo, string)
        Type elementType = objectType.GetGenericArguments()[0];
        // Cria o tipo concreto: List<elementType>
        Type listType = typeof(List<>).MakeGenericType(elementType);
        // Desserializa o token (que agora deverá ser um array) para uma List<elementType>
        object? list = token.ToObject(listType, serializer);
        return list;
    }

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        // Serializa normalmente (você pode optar por não incluir metadados de tipo para essa conversão)
        serializer.Serialize(writer, value);
    }
}
