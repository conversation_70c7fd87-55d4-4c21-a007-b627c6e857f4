using System.Collections;
using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Shared.JsonConverters;

public class IncludePrivateContractResolver : DefaultContractResolver
{
    public IncludePrivateContractResolver()
    {
        IgnoreSerializableInterface = true;
        IgnoreSerializableAttribute = true;
    }
    /// <summary>
    /// Retorna os membros serializáveis (propriedades e campos) considerando membros públicos e não públicos.
    /// </summary>
    protected override List<MemberInfo> GetSerializableMembers(Type objectType)
    {
        const BindingFlags flags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;

        // Considera propriedades (exceto indexadores) e campos
        IEnumerable<MemberInfo> properties = objectType.GetProperties(flags)
            .Where(p => p.GetIndexParameters().Length == 0)
            .Cast<MemberInfo>();

        IEnumerable<MemberInfo> fields = objectType.GetFields(flags)
            .Where(f => !f.IsStatic)
            .Cast<MemberInfo>();
        
        return properties.Concat(fields).ToList();
    }

    /// <summary>
    /// Para cada propriedade, se ela não for escrita, tenta localizar um setter privado ou um campo backing (pelo padrão "_nome").
    /// Se encontrado, marca a propriedade como writable e define um ValueProvider customizado para permitir a atribuição.
    /// Para coleções, força a substituição (Replace) para que o ValueProvider seja chamado.
    /// </summary>
    protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
    {
        JsonProperty property = base.CreateProperty(member, memberSerialization);

        ForceSerializeProperty(ref property, member, "ValidationResult");
        
        switch (member)
        {
            case PropertyInfo propInfo:
            {
                return ConvertPrivateReadonlyProperty(property, propInfo);
            }
            case FieldInfo field:
            {
                if (!property.Writable && field.IsInitOnly)
                {
                    property.Writable = true;
                    property.ValueProvider = new ReadonlyFieldValueProvider(field);
                    //property.TypeNameHandling = TypeNameHandling.None;
                }

                break;
            }
        }
        
        return property;
    }

    private static JsonProperty ConvertPrivateReadonlyProperty(JsonProperty property, PropertyInfo propInfo)
    {
        if (property.Writable)
        {
            return property;
        }

        // Tenta obter um setter privado
        MethodInfo? setter = propInfo.GetSetMethod(true);
        if (setter != null)
        {
            property.Writable = true;
        }
        else
        {
            // Procura o campo backing pelo padrão: "_" + nome com a primeira letra em minúsculo
            string candidateName = "_" + char.ToLowerInvariant(propInfo.Name[0]) + propInfo.Name[1..];
            FieldInfo? field = propInfo.DeclaringType?.GetField(candidateName, BindingFlags.Instance | BindingFlags.NonPublic);
            
            if (field == null || !(property.PropertyType?.IsAssignableFrom(field.FieldType) ?? false))
            {
                return property;
            }

            property.Writable = true;
            property.ValueProvider = new ReadonlyFieldValueProvider(field);
            // Para coleções (ou outros tipos imutáveis), forçamos a substituição para que o ValueProvider seja chamado
            property.ObjectCreationHandling = ObjectCreationHandling.Replace;
            property.TypeNameHandling = TypeNameHandling.None;
        }
        
        return property;
    }
    
    
    private static void ForceSerializeProperty(ref JsonProperty property, MemberInfo member, string propertyName, JsonConverter? converter = null)
    {
        if (!(property.PropertyName?.Equals(propertyName, StringComparison.OrdinalIgnoreCase) ?? false))
        {
            return;
        }

        property.Ignored = false;
        property.Converter = converter;
        property.Writable = true;
        string backingFieldName = $"<{property.PropertyName}>k__BackingField";
        FieldInfo? fieldInfo = member.DeclaringType?.GetField(backingFieldName, BindingFlags.Instance | BindingFlags.NonPublic);
        property.ValueProvider = new ReadonlyFieldValueProvider(fieldInfo!);
        property.ObjectCreationHandling = ObjectCreationHandling.Replace;
        property.TypeNameHandling = TypeNameHandling.None;
    }
    
    /// <summary>
    /// Um ValueProvider que utiliza reflection para ler e atribuir valores em campos,
    /// realizando conversão quando necessário, por exemplo, convertendo de ReadOnlyCollection para List.
    /// </summary>
    private sealed class ReadonlyFieldValueProvider : IValueProvider
    {
        private readonly FieldInfo _fieldInfo;

        public ReadonlyFieldValueProvider(FieldInfo fieldInfo)
        {
            _fieldInfo = fieldInfo;
        }

        public object? GetValue(object target)
        {
            return _fieldInfo.GetValue(target);
        }

        public void SetValue(object target, object? value)
        {
            // Se o valor não for nulo e não for diretamente atribuível ao tipo do campo,
            // e se o campo for do tipo List<T> e o valor for IEnumerable, converte-o para List<T>
            if (value != null &&
                !_fieldInfo.FieldType.IsInstanceOfType(value) &&
                _fieldInfo.FieldType.IsGenericType &&
                _fieldInfo.FieldType.GetGenericTypeDefinition() == typeof(List<>) &&
                value is IEnumerable)
            {
                Type elementType = _fieldInfo.FieldType.GetGenericArguments()[0];

                // Obtém os métodos Enumerable.Cast<T>() e Enumerable.ToList<T>()
                MethodInfo? castMethod = typeof(Enumerable)
                    .GetMethod("Cast", BindingFlags.Public | BindingFlags.Static)
                    ?.MakeGenericMethod(elementType);
                MethodInfo? toListMethod = typeof(Enumerable)
                    .GetMethod("ToList", BindingFlags.Public | BindingFlags.Static)
                    ?.MakeGenericMethod(elementType);

                // Converte o valor para IEnumerable<elementType> e em seguida para List<elementType>
                object? casted = castMethod?.Invoke(null, [value]);
                value = toListMethod?.Invoke(null, [casted]);
            }
            _fieldInfo.SetValue(target, value);
        }
    }
}

