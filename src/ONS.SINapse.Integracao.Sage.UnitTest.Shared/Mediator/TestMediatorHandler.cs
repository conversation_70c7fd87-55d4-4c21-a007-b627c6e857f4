using FluentValidation.Results;
using MassTransit.Testing;
using ONS.SINapse.Integracao.Sage.Shared.Mediator;
using ONS.SINapse.Integracao.Sage.Shared.Messages;
using static MassTransit.RequestExtensions;

namespace ONS.SINapse.UnitTest.Shared.Mediator;

public class TestMediatorHandler(ITestHarness testHarness) : IMediatorHandler
{
    public Task PublicarEventoAsync<T>(T evento, CancellationToken cancellationToken) where T : Event
    {
        return testHarness.Bus.Publish(evento, cancellationToken);
    }
    
    public async Task<ValidationResult> EnviarComandoAsync<T>(T comando, CancellationToken cancellationToken) where T : Command
    {
        MassTransit.Response<ValidationResult> result = await testHarness.Bus.Request<T, ValidationResult>(comando, cancellationToken);
        return result.Message;
    }

    public async Task<TResult> EnviarComandoAsync<TCommand, TResult>(TCommand comando, CancellationToken cancellationToken) where TCommand : Command<TResult> where TResult : CommandResult
    {
        MassTransit.Response<TResult> result = await testHarness.Bus.Request<TCommand, TResult>(comando, cancellationToken);
        return result.Message;
    }
}
