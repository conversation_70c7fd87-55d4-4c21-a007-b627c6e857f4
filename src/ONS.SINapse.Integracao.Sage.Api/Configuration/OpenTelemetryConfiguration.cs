using MassTransit.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using OpenTelemetry;
using OpenTelemetry.Exporter;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace ONS.SINapse.Integracao.Sage.Api.Configuration;

public static class OpenTelemetryConfiguration
{
    private const string DefaultServiceVariableName = "OTEL_EXPORTER_OTLP_ENDPOINT";
    private const OtlpExportProtocol DefaultExportProtocol = OtlpExportProtocol.Grpc;

    public static WebApplicationBuilder AddSinapseOpenTelemetry(this WebApplicationBuilder builder)
    {
        OpenTelemetrySettings options = builder.Configuration
                          .GetSection(nameof(OpenTelemetrySettings))
                          .Get<OpenTelemetrySettings>()
                      ?? throw new NullReferenceException(nameof(OpenTelemetrySettings));

        if (!options.ServiceEnabled)
        {
            return builder;
        }

        string? endpoint = Environment.GetEnvironmentVariable(DefaultServiceVariableName);

        if (string.IsNullOrEmpty(endpoint))
        {
            return builder;
        }

        ResourceBuilder resourceBuilder = ResourceBuilder.CreateDefault()
            .AddService(options.ServiceName, options.ServiceVersion)
            .AddTelemetrySdk();

        builder.Services.AddOpenTelemetry()
            .AddSinapseMetrics(resourceBuilder, options.ServiceName)
            .AddSinapseTrace(resourceBuilder, options.ServiceName);

        return builder;
    }

    public static WebApplication UseSinapseOpenTelemetry(this WebApplication app)
    {
        OpenTelemetrySettings options = app.Services.GetRequiredService<IOptions<OpenTelemetrySettings>>().Value;

        if (!options.ServiceEnabled)
        {
            return app;
        }

        app.MapPrometheusScrapingEndpoint();
        app.UseOpenTelemetryPrometheusScrapingEndpoint();

        return app;
    }

    private static OpenTelemetryBuilder AddSinapseMetrics(this OpenTelemetryBuilder openTelemetryBuilder, ResourceBuilder resourceBuilder, string serviceName)
    {
        openTelemetryBuilder.WithMetrics(builder =>
        {
            builder.AddMeter(serviceName)
                .SetResourceBuilder(resourceBuilder)
                .AddHttpClientInstrumentation()
                .AddAspNetCoreInstrumentation()
                .AddRuntimeInstrumentation()
                .AddProcessInstrumentation()
                .AddPrometheusExporter()
                .AddOtlpExporter(exporter =>
                {
                    exporter.Protocol = DefaultExportProtocol;
                });
        });

        return openTelemetryBuilder;
    }

    private static OpenTelemetryBuilder AddSinapseTrace(this OpenTelemetryBuilder openTelemetryBuilder, ResourceBuilder resourceBuilder, string serviceName)
    {
        openTelemetryBuilder.WithTracing(tracerProviderBuilder =>
        {
            tracerProviderBuilder
                .AddSource(serviceName)
                .SetResourceBuilder(resourceBuilder)
                .AddAspNetCoreInstrumentation((options) =>
                {
                    options.Filter = (httpContext) =>
                    {
                        var routes = new List<string>
                            {
                                "/swagger",
                                "/health"
                            };

                        string path = httpContext.Request.Path.Value!.ToLower();
                        return !routes.Any(route => path.Contains(route));
                    };

                    options.RecordException = true;
                })
                .AddHttpClientInstrumentation(options =>
                {
                    options.FilterHttpRequestMessage = (request) =>
                    {
                        var excludedRoutes = new List<string>
                        {
                            "/swagger",
                            "/health",
                            "/favicon.ico"
                        };

                        string path = request.RequestUri?.AbsolutePath.ToLower() ?? "";

                        return !excludedRoutes.Any(route => path.Contains(route));
                    };

                    options.RecordException = true;
                })
                .AddAspNetCoreInstrumentation()
                .AddRedisInstrumentation()
                .SetErrorStatusOnException()
                .AddOtlpExporter(exporter =>
                {
                    exporter.Protocol = DefaultExportProtocol;
                });
        });

        return openTelemetryBuilder;
    }
}
