using ONS.SINapse.Integracao.Sage.Shared.ConfigIt;

namespace ONS.SINapse.Integracao.Sage.Api.Configuration;

public static class ApplicationConfiguration
{
    public static IWebHostBuilder ConfigureSinapseDadosApplication(this IWebHostBuilder webHostBuilder)
    {
        webHostBuilder
            .ConfigureAppConfiguration((webHostContext, config) =>
            {
                config.SetBasePath(webHostContext.HostingEnvironment.ContentRootPath)
                    .AddJsonFile("appsettings.json", true, true)
                    .AddJsonFile($"appsettings.{webHostContext.HostingEnvironment.EnvironmentName}.json", true, true)
                    .AddEnvironmentVariables();

                string env = webHostContext.HostingEnvironment.EnvironmentName;

                if (env != "Production")
                {
                    return;
                }

                config.ConfigureConfigIt();
            })
            .ConfigureKestrel(_ => { });
        
        return webHostBuilder;
    }
    
    private static void ConfigureConfigIt(this IConfigurationBuilder configurationBuilder)
    {
        IConfigurationSource? appsettingsSource = configurationBuilder.Sources
            .FirstOrDefault(x => x.GetType().Name.Contains("JsonConfigurationSource"));

        IConfigurationProvider? config = appsettingsSource?.Build(configurationBuilder);
        if (config is null)
        {
            return;
        }

        using ConfigurationRoot appRoot = new ([config]);

        bool ignore = bool.Parse(appRoot.GetSection("ONS")["IgnoreConfigIT"] ?? "false");

        if (ignore)
        {
            return;
        }

        configurationBuilder.ConfigureEnviromentConfigIt();
    }

    
    private static void ConfigureEnviromentConfigIt(this IConfigurationBuilder configurationBuilder)
    {
        configurationBuilder.AddEnvironmentVariables();
        
        IConfigurationRoot environmentVariables = configurationBuilder.Build();
        
        configurationBuilder.AddConfigItConfiguration(options =>
        {
            options["-amb"] = environmentVariables["ConfigITamb"] ?? string.Empty;
            options["-user"] = environmentVariables["ConfigITuser"] ?? string.Empty;
            options["-password"] = environmentVariables["ConfigITpwd"] ?? string.Empty;
            options["-r"] = environmentVariables["ConfigITr"] ?? string.Empty;
        });
    }
}
