{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "http://localhost:7073", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "https://otel-ce-programacao-operacao-shared.apps.ocpd.ons.org.br"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:7221;http://localhost:5027", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "https://otel-ce-programacao-operacao-shared.apps.ocpd.ons.org.br"}}}}