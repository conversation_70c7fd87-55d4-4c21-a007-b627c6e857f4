using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.Shared.Identity;
using ONS.SINapse.Integracao.Sage.Shared.Identity.Claims;
using ONS.SINapse.Integracao.Sage.Shared.Mediator;

namespace ONS.SINapse.Integracao.Sage.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.Sincronizador)]
public class SolicitacaoController : ControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;
    
    public SolicitacaoController(IMediatorHandler mediatorHandler)
    {
        _mediatorHandler = mediatorHandler;
    }

    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> CriarEmLote(
        [FromBody] CadastrarSolicitacaoEvent cadastrarSolicitacaoEvent,
        CancellationToken cancellationToken)
    {
        await _mediatorHandler
            .PublicarEventoAsync<CadastrarSolicitacaoEvent>(cadastrarSolicitacaoEvent,
                cancellationToken);

        return Ok();
    }
}
