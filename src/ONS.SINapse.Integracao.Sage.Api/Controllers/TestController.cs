using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Integracao.Sage.Shared.Identity;
using ONS.SINapse.Integracao.Sage.Shared.Identity.Claims;

namespace ONS.SINapse.Integracao.Sage.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private const string HealthStatus = "Healthy";

    [HttpGet(Name = "check")]
    public string Get() => HealthStatus;
}
