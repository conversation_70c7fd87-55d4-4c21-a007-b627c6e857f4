using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Integracao.Sage.ExternalServices.Services;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Identity;
using ONS.SINapse.Integracao.Sage.Shared.Identity.Claims;

namespace ONS.SINapse.Integracao.Sage.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.Sincronizador)]
public class DataSetController : ControllerBase
{
    private readonly ISinapseDadosDatasetService _sinapseDadosDatasetService;

    public DataSetController(ISinapseDadosDatasetService sinapseDadosDatasetService)
    {
        _sinapseDadosDatasetService = sinapseDadosDatasetService;
    }

    [HttpGet]
    public async Task<ICollection<DatasetItemDto>> Get()
    {
        var query = new QueryDatasetDto() { Query = "id==BABJS-2RT3_CHF" };
        ICollection<DatasetItemDto> dataset = await _sinapseDadosDatasetService.GetEquipamentoDataset(query);
        return dataset;
    }
}
