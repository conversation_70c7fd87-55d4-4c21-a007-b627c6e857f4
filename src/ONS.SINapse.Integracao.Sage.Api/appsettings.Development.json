{"ONS": {"IgnoreConfigIT": true, "Authorization": {"Issuer": "https://popons.amcom.com.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==", "RsaPublicExponent": "AQAB"}, "PopAuth": {"Origin": "http://local.amcom.com.br", "TokenURL": "https://popons.amcom.com.br/ons.pop.federation/oauth2/token", "ClientId": "SINAPSE", "Username": "amcom\\popons", "Password": "", "GrantTypeRefreshToken": "refresh_token", "GrantTypeAccessToken": "password"}}, "ApiOptions": {"CustomHeaders": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ONS.SINapse.Integracao.Sage.Api"}, "ServiceUri": "https://localhost:7074", "Authentication": {"ServiceUri": "https://popons.amcom.com.br/ons.pop.federation/oauth2/token", "ApplicationCacheExpiration": 300, "ApplicationName": "SINAPSE", "ApplicationOrigin": "http://local.amcom.com.br"}}, "ConnectionStrings": {"Redis": "localhost:6379,defaultDatabase=0,password=teste"}, "ApplicationSettings": {"SinapseIntegracaoSageHealthCheckUrl": "https://localhost:7221/health", "SinapseDadosHealthCheckUrl": "https://localhost:7074/health", "SinapseHealthCheckUrl": "", "SinapseIntegracaoHealthCheckUrl": ""}, "OpenTelemetrySettings": {"ServiceName": "ons.sinapse.integracao.sage.api", "ServiceVersion": "1.0.0", "ServiceEnabled": true}, "KafkaSettings": {"Ativo": true, "SaslUsername": "", "SaslPassword": "", "RequireAuth": false, "BootstrapServers": "localhost:9092", "SaslMechanism": "", "SecurityProtocol": "", "SslCaPem": "", "SslKeystorePassword": ""}, "KafkaQueueSettings": {"GroupId": "ONS.SINAPSE.INTEGRACAO.SAGE", "Topics": {"CadastroSolicitacaoSinapse": "ONS.SINAPSE.CADASTRO_SOLICITACAO", "CadastroSolicitacaoPI": "ONS.SINAPSE.CADASTRO_SOLICITACAO_PI.TST", "Health": "ONS.SINAPSE.INTEGRACAO.SAGE.HEALTHCHECK"}}, "RedisCacheSettings": {"CacheExpiration": 1800}}