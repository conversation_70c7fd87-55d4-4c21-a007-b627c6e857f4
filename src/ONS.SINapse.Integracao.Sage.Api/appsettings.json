{"ONS": {"IgnoreConfigIT": false, "Authorization": {"Issuer": "https://poptst.ons.org.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==", "RsaPublicExponent": "AQAB"}, "PopAuth": {"Origin": "http://local.ons.org.br", "TokenURL": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "ClientId": "SINAPSE", "Username": "ons\\teste-aplic1", "Password": "", "GrantTypeRefreshToken": "refresh_token", "GrantTypeAccessToken": "password"}}, "ApiOptions": {"CustomHeaders": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ONS.SINapse.Api"}, "ServiceUri": "https://sinapse-dados-api-tst.apps.ocpd.ons.org.br", "Authentication": {"ServiceUri": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "ApplicationCacheExpiration": 300, "ApplicationName": "SINAPSE", "ApplicationOrigin": "http://local.ons.org.br"}}, "ConnectionStrings": {"Redis": ""}, "ApplicationSettings": {"SinapseIntegracaoSageHealthCheckUrl": "", "SinapseDadosHealthCheckUrl": "", "SinapseHealthCheckUrl": "", "SinapseIntegracaoHealthCheckUrl": ""}, "OpenTelemetrySettings": {"ServiceName": "ons.sinapse.integracao.sage.api", "ServiceVersion": "1.0.0", "ServiceEnabled": true}, "KafkaSettings": {"Ativo": true, "SaslUsername": "usr-sinapse", "SaslPassword": "", "RequireAuth": true, "BootstrapServers": "kafka-cluster-kafka-http-bootstrap-kafka.apps.ocpd.ons.org.br:443", "SaslMechanism": "ScramSha512", "SecurityProtocol": "SaslSsl", "SslCaPem": "", "SslKeystorePassword": ""}, "KafkaQueueSettings": {"GroupId": "ONS.SINAPSE.INTEGRACAO.SAGE", "Topics": {"CadastroSolicitacaoSinapse": "ONS.SINapse.CadastroSolicitacao", "CadastroSolicitacaoPI": "ONS.SINapse.CadastroSolicitacaoSage", "Health": "ONS.SINapse.HealthCheckSage"}}, "RedisCacheSettings": {"CacheExpiration": 1800}}