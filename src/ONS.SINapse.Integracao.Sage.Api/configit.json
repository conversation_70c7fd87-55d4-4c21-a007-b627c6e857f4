{"KafkaSettings:BootstrapServers": {"depara": "Kafka_BootstrapServers_Openshift", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SaslMechanism": {"depara": "Kafka_SaslMechanism", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SecurityProtocol": {"depara": "Kafka_SecurityProtocol", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SslCaPem": {"depara": "Kafka_SslCaPem", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SslKeystorePassword": {"depara": "Kafka_SslKeystorePassword", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaQueueSettings:Topics:CadastroSolicitacaoPI": {"depara": "KafkaQueueSettings.Topics.CadastroSolicitacaoPI", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ApiOptions:Authentication:ServiceUri": {"depara": "ONS.SINapse.IntegracaoSage.Api.ApiOptions.Authentication.ServiceUri", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ApiOptions:ServiceUri": {"depara": "ONS.SINapse.IntegracaoSage.Api.ApiOptions.ServiceUri", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ApiOptions:Authentication:ApplicationCacheExpiration": {"depara": "ONS.SINapse.IntegracaoSage.Api.ApiOptions:Authentication.ApplicationCacheExpiration", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ApplicationSettings:SinapseDadosHealthCheckUrl": {"depara": "ONS.SINapse.IntegracaoSage.Api.ApplicationSettings.SinapseDadosHealthCheckUrl", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ApplicationSettings:SinapseIntegracaoSageHealthCheckUrl": {"depara": "ONS.SINapse.IntegracaoSage.Api.ApplicationSettings.SinapseIntegracaoSageHealthCheckUrl", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaQueueSettings:GroupId": {"depara": "ONS.SINapse.IntegracaoSage.Api.KafkaQueueSettings.GroupId", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:Ativo": {"depara": "ONS.SINapse.IntegracaoSage.Api.KafkaSettings.Ativo", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SaslPassword": {"depara": "ONS.SINapse.IntegracaoSage.Api.KafkaSettings.SaslPassword", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SaslUsername": {"depara": "ONS.SINapse.IntegracaoSage.Api.KafkaSettings.SaslUsername", "arquivo": "appsettings.json", "kind": "JsonFile"}, "RedisCacheSettings:CacheExpiration": {"depara": "RedisCacheSettings.CacheExpiration", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ONS:Authorization:Issuer": {"depara": "SINAPSE_ONS.Authorization.Issuer", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ONS:Authorization:RsaModulus": {"depara": "SINAPSE_ONS.Authorization.RsaModulus", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ONS:Authorization:RsaPublicExponent": {"depara": "SINAPSE_ONS.Authorization.RsaPublicExponent", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ConnectionStrings:Redis": {"depara": "SINAPSE_ONS.ConnectionStrings.Redis", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ONS:PopAuth:Origin": {"depara": "SINAPSE_ONS.PopAuth.Origin", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ONS:PopAuth:TokenURL": {"depara": "SINAPSE_ONS.PopAuth.TokenURL", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaQueueSettings:Topics:CadastroSolicitacaoSinapse": {"depara": "SinapseKafkaOptions.Topicos.Enviar", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ONS:PopAuth:Username": {"depara": "SinapseOptions.Authentication.Username", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ONS:PopAuth:Password": {"depara": "SinapseOptions.Authentication.Password", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ApplicationSettings:SinapseHealthCheckUrl": {"depara": "SINAPSE_ONS.ApplicationSettings.HealthCheckUrl", "arquivo": "appsettings.json", "kind": "JsonFile"}, "ApplicationSettings:SinapseIntegracaoHealthCheckUrl": {"depara": "SINAPSE_ONS.ApplicationSettings.SinapseIntegracaoHealthCheckUrl", "arquivo": "appsettings.json", "kind": "JsonFile"}}