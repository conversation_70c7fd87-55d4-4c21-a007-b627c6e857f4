using ONS.SINapse.Integracao.Sage.Api.Configuration;
using ONS.SINapse.Integracao.Sage.CrossCutting;
using ONS.SINapse.Integracao.Sage.Shared.Extensions;
using ONS.SINapse.Integracao.Sage.Shared.Middleware;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

builder.WebHost.ConfigureSinapseDadosApplication();
builder.Services.AddSwaggerConfiguration();
builder.Services.AddCors(options => {
    options.AddPolicy("CORS_POLICY",
        corsPolicyBuilder => corsPolicyBuilder
            .SetIsOriginAllowed(_ => true)
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .Build()
    );
});

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

DependencyInjector.Register(builder.Services, builder.Configuration);

builder.Services.AddMassTransitWithKafka(
    builder.Configuration,
    configureConsumers: MassTransitInjector.ConfigureConsumers(),
    configureProducers: MassTransitInjector.ConfigureProducers(builder.Configuration),
    configureBus: MassTransitInjector.ConfigureBus()
);

builder.Services.AddHttpContextAccessor();
builder.Services.AddTransient<GlobalExceptionHandlerMiddleware>();

builder.AddSinapseOpenTelemetry();

WebApplication app = builder.Build();

app.UseSinapseOpenTelemetry();

// Configura��o do Swagger
if (app.Environment.IsDevelopment())
{
    app.UseSwaggerConfiguration();
}

app.UseHttpsRedirection();
app.UseGlobalExceptionHandlerMiddleware();

// --- Ordem Correta do Pipeline ---
app.UseRouting(); // 1. Roteamento primeiro

app.UseCors("CORS_POLICY"); // Considere adicionar UseCors aqui se aplic�vel

app.UseAuthentication(); // 2. Autentica��o (se houver)
app.UseAuthorization(); // 3. Autoriza��o (se houver)

app.MapControllers(); // 4. Mapeamento dos Endpoints/Controllers

app.UseSinapseHealthCheck();

await app.RunAsync();
