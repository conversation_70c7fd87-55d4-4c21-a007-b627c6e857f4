<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="OpenTelemetry" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" />
		<PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Process" />
		<PackageReference Include="OpenTelemetry.Instrumentation.StackExchangeRedis" />
		<PackageReference Include="Swashbuckle.AspNetCore" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\ONS.SINapse.Integracao.Sage.CrossCutting\ONS.SINapse.Integracao.Sage.CrossCutting.csproj" />
	</ItemGroup>

</Project>
