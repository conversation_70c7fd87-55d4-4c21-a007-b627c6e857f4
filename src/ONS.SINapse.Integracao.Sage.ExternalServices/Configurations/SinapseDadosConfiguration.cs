using System.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.Integracao.Sage.ExternalServices.Services;
using ONS.SINapse.Integracao.Sage.Shared.Extensions;
using ONS.SINapse.Integracao.Sage.Shared.Settings;
using Refit;

namespace ONS.SINapse.Integracao.Sage.ExternalServices.Configurations;

public static class SinapseDadosConfiguration
{

    public static IServiceCollection AddSinapseDados(this IServiceCollection services, IConfiguration configuration)
    {
        services
            .AddSinapseDadosDatasetService(configuration);

        return services;
    }

    private static IServiceCollection AddSinapseDadosDatasetService(this IServiceCollection services, IConfiguration configuration)
    {
        ApiOptions? syncSettings = configuration.GetSection(nameof(ApiOptions)).Get<ApiOptions>();
        ArgumentNullException.ThrowIfNull(syncSettings);

        string baseUrl = syncSettings.ServiceUri;
        ArgumentNullException.ThrowIfNull(syncSettings);

        var refitOptions = new RefitSettings(new NewtonsoftJsonContentSerializer(new JsonSerializerSettings()
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        }))
        {
            CollectionFormat = CollectionFormat.Multi
        };

        services
            .AddRefitClient<ISinapseDadosDatasetService>(refitOptions)
            .ConfigureHttpClient(options =>
            {
                options.BaseAddress = new Uri(baseUrl);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (_, cert, _, _) => cert != null,
                UseDefaultCredentials = true,
                AllowAutoRedirect = true,
                PreAuthenticate = true,
                Credentials = CredentialCache.DefaultNetworkCredentials,
                DefaultProxyCredentials = CredentialCache.DefaultNetworkCredentials
            })
            .AddAuthenticationRequestHandler<ApiOptions>(configuration);

        return services;
    }
}
