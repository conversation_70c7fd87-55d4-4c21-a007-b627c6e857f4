using System.Diagnostics;
using FluentValidation;
using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.ExternalServices.Services;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Factories;
using ONS.SINapse.Integracao.Sage.Shared.Parses;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using OpenTelemetry.Context.Propagation;

namespace ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Handlers;

public class CadastrarSolicitacaoEventHandler : IConsumer<CadastrarSolicitacaoEvent>
{
    private const string ActivitySourceName = "ons.sinapse.integracao.sage.api";
    private static readonly ActivitySource ActivitySource = new(ActivitySourceName);

    private readonly ILogger<CadastrarSolicitacaoEventHandler> _logger;
    private readonly ITopicProducer<CadastrarSolicitacaoSinapseEvent> _producer;
    private readonly IValidator<CadastrarSolicitacaoEvent> _cadastrarSolicitacaoEventValidator;
    private readonly ICacheDuplicationValidator _cacheDuplicationValidator;
    private readonly ISinapseDadosDatasetService _sinapseDadosDatasetService;

    public CadastrarSolicitacaoEventHandler(
        ILogger<CadastrarSolicitacaoEventHandler> logger,
        ITopicProducer<CadastrarSolicitacaoSinapseEvent> producer,
        IValidator<CadastrarSolicitacaoEvent> cadastrarSolicitacaoEventValidator,
        ICacheDuplicationValidator cacheDuplicationValidator,
        ISinapseDadosDatasetService sinapseDadosDatasetService)
    {
        _logger = logger;
        _producer = producer;
        _cadastrarSolicitacaoEventValidator = cadastrarSolicitacaoEventValidator;
        _cacheDuplicationValidator = cacheDuplicationValidator;
        _sinapseDadosDatasetService = sinapseDadosDatasetService;
    }

    public Task Consume(ConsumeContext<CadastrarSolicitacaoEvent> context)
    {
        Task.Run(async () =>
        {
            ActivityContext? activityContext = ExtractActivityContext(context.Headers);

            using Activity? activityConsumer = ActivitySource.StartActivity("CadastrarSolicitacaoEventHandler", ActivityKind.Internal, activityContext ?? default);

            try
            {
                _logger.LogInformation("[CadastrarSolicitacaoEventHandler] Mensagem consumida {Event}: {Mensagem}",
                    nameof(CadastrarSolicitacaoEvent), context.Message.Message);

                activityConsumer?.AddTag("mensagem", context.Message.Message);

                if (!await ValidarEventoAsync(context, activityContext))
                {
                    return;
                }

                MensagemSolicitacaoDto mensagem = ParseMessage(context, activityContext);

                if (!await ValidarDuplicidadeAsync(mensagem, activityContext))
                {
                    return;
                }

                DatasetItemDto? dataset = await BuscarDataset(mensagem, activityContext);
                if (dataset is null)
                {
                    return;
                }

                CadastroSolicitacaoDto solicitacao = CadastroSolicitacaoFactory.FromDatasetItem(dataset, mensagem);

                var evento = new CadastrarSolicitacaoSinapseEvent([solicitacao]);

                using Activity? activityProducer = ActivitySource.StartActivity("Producer", ActivityKind.Producer, activityContext ?? default);
                activityProducer?.SetTag("producer.event.name", nameof(CadastrarSolicitacaoSinapseEvent));
                activityProducer?.SetTag("producer.event.message", solicitacao.Mensagem);

                await _producer.Produce(evento);

                activityProducer?.SetTag("published", true);

                _logger.LogInformation("[CadastrarSolicitacaoEventHandler] Mensagem publicada {Event}", nameof(CadastrarSolicitacaoSinapseEvent));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao processar evento: {Mensagem}", context.Message);
                activityConsumer?.SetStatus(ActivityStatusCode.Error);
                activityConsumer?.SetTag("exception.message", ex.Message);
                activityConsumer?.SetTag("exception.stacktrace", ex.StackTrace);
                return;
            }
        });

        return Task.CompletedTask;
    }

    private async Task<bool> ValidarEventoAsync(ConsumeContext<CadastrarSolicitacaoEvent> context, ActivityContext? activityContext)
    {
        using Activity? activityValidate = ActivitySource.StartActivity("Validate", ActivityKind.Internal, activityContext ?? default);
        activityValidate?.AddTag("mensagem", context.Message.Message);

        FluentValidation.Results.ValidationResult validation = await _cadastrarSolicitacaoEventValidator.ValidateAsync(context.Message);
        if (validation.IsValid)
        {
            activityValidate?.SetTag("IsValid", true);
            return true;
        }

        string erros = string.Join(" | ", validation.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}"));
        _logger.LogError("[CadastrarSolicitacaoEventHandler] Erros de validação encontrados: {Errors}", erros);

        activityValidate?.SetTag("validacao.erros", erros);
        activityValidate?.SetTag("validacao.fail", true);
        activityValidate?.SetStatus(ActivityStatusCode.Error);

        return false;
    }

    private async Task<bool> ValidarDuplicidadeAsync(MensagemSolicitacaoDto mensagem, ActivityContext? activityContext)
    {
        using Activity? activityValidarDuplicidade = ActivitySource.StartActivity("ValidarDuplicidade", ActivityKind.Internal, activityContext ?? default);
        activityValidarDuplicidade?.SetTag("IDO", mensagem.Ido);
        activityValidarDuplicidade?.SetTag("Ação", mensagem.Acao);

        bool isNovaSolicitacao = await _cacheDuplicationValidator.ValidateAndSetAsync<bool>(mensagem.Ido, mensagem.Acao);
        if (isNovaSolicitacao)
        {
            activityValidarDuplicidade?.SetTag("isNovaSolicitacao", true);
            return true;
        }

        _logger.LogError("[CadastrarSolicitacaoEventHandler] Já existe uma solicitação para esses dados: IDO {IDO} e Ação {Acao}", mensagem.Ido, mensagem.Acao);
        activityValidarDuplicidade?.SetStatus(ActivityStatusCode.Error);
        activityValidarDuplicidade?.SetTag("isNovaSolicitacao", false);
        activityValidarDuplicidade?.SetTag("error.message", "Já existe uma solicitação para esses dados");

        return false;
    }

    private async Task<DatasetItemDto?> BuscarDataset(MensagemSolicitacaoDto mensagem, ActivityContext? activityContext)
    {
        using Activity? activityDataset = ActivitySource.StartActivity("GetEquipamentoDataset", ActivityKind.Internal, activityContext ?? default);

        activityDataset?.SetTag("ido", mensagem.Ido);

        try
        {
            var query = new QueryDatasetDto() { Query = $"id=={mensagem.Ido}" };
            ICollection<DatasetItemDto> datasetItem = await _sinapseDadosDatasetService.GetEquipamentoDataset(query);
            if (datasetItem is null || datasetItem.Count == 0)
            {
                _logger.LogError("[CadastrarSolicitacaoEventHandler] Não foi encontrado nenhum Dataset para o IDO informado: IDO {IDO}", mensagem.Ido);
                activityDataset?.SetTag("dataset.error", "dataset nao encontrado");
                activityDataset?.SetStatus(ActivityStatusCode.Error);
                return null;
            }

            if (datasetItem.Count > 1)
            {
                _logger.LogError("[CadastrarSolicitacaoEventHandler] Foi encontrado mais de um equipamento para o IDO informado: IDO {IDO}", mensagem.Ido);
                activityDataset?.SetTag("dataset.error", "multiplos equipamentos para mesmo ido");
                activityDataset?.SetStatus(ActivityStatusCode.Error);
                return null;
            }

            activityDataset?.SetTag("dataset", "dataset encontrado com sucesso");
            return datasetItem.First();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[CadastrarSolicitacaoEventHandler] Erro ao buscar Dataset para o IDO informado: IDO {IDO}", mensagem.Ido);
            activityDataset?.SetTag("exception.message", ex.Message);
            activityDataset?.SetTag("exception.stacktrace", ex.StackTrace);
            activityDataset?.SetStatus(ActivityStatusCode.Error);
            return null;
        }
    }

    private MensagemSolicitacaoDto ParseMessage(ConsumeContext<CadastrarSolicitacaoEvent> context, ActivityContext? activityContext)
    {
        using Activity? activityParse = ActivitySource.StartActivity("ParseMessage", ActivityKind.Internal, activityContext ?? default);
        activityParse?.SetTag("message", context.Message.Message);

        MensagemSolicitacaoDto mensagem = MensagemSolicitacaoParser.Parse(context.Message.Message)!;

        activityParse?.SetTag("mensagem.ido", mensagem.Ido);
        activityParse?.SetTag("mensagem.acao", mensagem.Acao);

        return mensagem;
    }

    private static ActivityContext? ExtractActivityContext(Headers? headers)
    {
        if (headers is null)
        {
            return null;
        }

        TextMapPropagator propagator = Propagators.DefaultTextMapPropagator;

        var extractedHeaders = new Dictionary<string, string>();
        foreach (HeaderValue header in headers)
        {
            if (header.Key.Equals("traceparent", StringComparison.OrdinalIgnoreCase) ||
                header.Key.Equals("tracestate", StringComparison.OrdinalIgnoreCase))
            {
                object rawValue = header.Value;
                if (rawValue is not null)
                {
                    extractedHeaders[header.Key] = rawValue.ToString()!;
                }
            }
        }

        PropagationContext parentContext = propagator.Extract(
            default,
            extractedHeaders,
            (dict, key) => dict.TryGetValue(key, out string value) ? [value] : Array.Empty<string>()
        );

        return parentContext.ActivityContext;
    }
}
