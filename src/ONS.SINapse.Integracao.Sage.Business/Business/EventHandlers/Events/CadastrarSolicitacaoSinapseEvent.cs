using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Messages;

namespace ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
public class CadastrarSolicitacaoSinapseEvent : Event
{
    public CadastrarSolicitacaoSinapseEvent(List<CadastroSolicitacaoDto> solicitacoes)
    {
        Solicitacoes = solicitacoes;
    }

    public List<CadastroSolicitacaoDto> Solicitacoes { get; set; }
}
