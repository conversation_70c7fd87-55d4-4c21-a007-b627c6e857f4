using FluentValidation;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.Shared.Parses;

namespace ONS.SINapse.Integracao.Sage.Business.Business.Validators;

public partial class CadastrarSolicitacaoEventValidator : AbstractValidator<CadastrarSolicitacaoEvent>
{
    public CadastrarSolicitacaoEventValidator()
    {
        RuleFor(x => x.Message)
            .NotEmpty().WithMessage("A mensagem não pode estar vazia.")
            .Must(message => MensagemSolicitacaoParser.Parse(message) != null)
            .WithMessage("A mensagem não segue o padrão esperado: 'Nota de operação -> SINapse #AÇÃO#IDO# pelo usuário #reger+usuario#'.");
    }
}
