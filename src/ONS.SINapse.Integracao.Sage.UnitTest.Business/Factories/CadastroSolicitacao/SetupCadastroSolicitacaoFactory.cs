using ONS.SINapse.Integracao.Sage.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.Factories.CadastroSolicitacao;

[Collection(nameof(CadastroSolicitacaoFactoryCollection))]
public partial class CadastroSolicitacaoFactoryTest : IDisposable
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public CadastroSolicitacaoFactoryTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    public void Dispose()
    {
        _dependencyInjectorFactory.Dispose();
    }
}
