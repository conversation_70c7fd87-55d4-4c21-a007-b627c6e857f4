using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Factories;
using ONS.SINapse.Integracao.Sage.UnitTest.Business.Fixtures.Collections;
using Shouldly;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.Factories.CadastroSolicitacao;

public partial class CadastroSolicitacaoFactoryTest
{
    [Fact]
    public void FromDatasetItem_DeveRetornarCadastroSolicitacaoDtoComDadosCorretos()
    {
        // Arrange
        var item = new DatasetItemDto
        {
            Id = "item-001",
            Origin = new ManeuverObjectDto("SE", "COSR-SE"),
            Destination = new ManeuverObjectDto ("NE", "COSR-NE"),
            Local = new ManeuverObjectDto ("S", "COSR-S"),
            Description = "Teste",
            Label = "Rótulo"
        };

        var mensagemSolicitacao = new MensagemSolicitacaoDto
        {
            Acao = "Desligar",
            Usuario = "kaio.kopko"
        };

        // Act
        CadastroSolicitacaoDto resultado = CadastroSolicitacaoFactory.FromDatasetItem(item, mensagemSolicitacao);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Id.ShouldNotBeEmpty();
        resultado.Origem.Codigo.ShouldBe("SE");
        resultado.Origem.Nome.ShouldBe("COSR-SE");
        resultado.Destino.Codigo.ShouldBe("NE");
        resultado.Destino.Nome.ShouldBe("COSR-NE");
        resultado.Local.Codigo.ShouldBe("S");
        resultado.Local.Nome.ShouldBe("COSR-S");
        resultado.SistemaDeOrigem.ShouldBe("SAGE");
        resultado.Mensagem.ShouldBe("Desligar COSR-S");
        resultado.Tags.Count.ShouldBe(1);
        resultado.Tags[0].ShouldBe("tensão_equipamento");
        resultado.Usuario.Login.ShouldBe("kaio.kopko");
        resultado.Usuario.Sid.ShouldBe("kaio.kopko");
        resultado.Usuario.Nome.ShouldBe("Kaio Kopko");
        resultado.RequerAprovacaoEnvio.ShouldBeTrue();
    }

    [Fact]
    public void FromDatasetItem_DeveFormatarNomeDoUsuarioCorretamente()
    {
        // Arrange
        var item = new DatasetItemDto
        {
            Origin = new ManeuverObjectDto("SE", "COSR-SE"),
            Destination = new ManeuverObjectDto("NE", "COSR-NE"),
            Local = new ManeuverObjectDto("S", "COSR-S")
        };

        var mensagemSolicitacao = new MensagemSolicitacaoDto
        {
            Acao = "Ligar",
            Usuario = "kaio.kopko"
        };

        // Act
        CadastroSolicitacaoDto resultado = CadastroSolicitacaoFactory.FromDatasetItem(item, mensagemSolicitacao);

        // Assert
        resultado.Usuario.Nome.ShouldBe("Kaio Kopko");
    }
}
