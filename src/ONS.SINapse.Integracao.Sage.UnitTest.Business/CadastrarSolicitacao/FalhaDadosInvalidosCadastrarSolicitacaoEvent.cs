using Microsoft.Extensions.DependencyInjection;
using Moq;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.ExternalServices.Services;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Mediator;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared.Fxtures;
using Shouldly;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.CadastrarSolicitacao;
public partial class CadastrarSolicitacaoEventTest
{


    [Fact(DisplayName = "Não deve publicar mensagem se a solicitação for duplicada")]
    [Trait("EventHandler", "CadastrarSolicitacao")]
    public async Task NaoDevePublicarMensagemSeSolicitacaoForDuplicada()
    {
        // Arrange
        IMediatorHandler mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var mensagem = new CadastrarSolicitacaoEvent
        {
            Message = "Nota de operação -> SINapse #DESLIGAR#RSGRA2_230_BC5# pelo usuario #reger+kaio.kopko#"
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<ICacheDuplicationValidator>()
            .Setup(v => v.ValidateAndSetAsync<bool>(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        // Act
        await mediator.PublicarEventoAsync(mensagem, CancellationToken.None);

        // Assert
        bool eventPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEvent>();
        bool eventConsumed = await _dependencyInjectorFactory.Harness.Consumed.Any<CadastrarSolicitacaoEvent>();
        bool eventKafkaPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoSinapseEvent>();

        _dependencyInjectorFactory.Mocker.GetMock<ICacheDuplicationValidator>()
           .Verify(v => v.ValidateAndSetAsync<bool>(It.IsAny<string>(), It.IsAny<string>()), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetService>()
           .Verify(repository => repository.GetEquipamentoDataset(It.IsAny<QueryDatasetDto>()),
           Times.Never);

        eventPublished.ShouldBeTrue();
        eventConsumed.ShouldBeTrue();
        eventKafkaPublished.ShouldBeFalse();
    }

    [Fact(DisplayName = "Não deve publicar mensagem se nenhum dataset for encontrado")]
    [Trait("EventHandler", "CadastrarSolicitacao")]
    public async Task NaoDevePublicarMensagemSeNenhumDatasetForEncontrado()
    {
        // Arrange
        IMediatorHandler mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var mensagem = new CadastrarSolicitacaoEvent
        {
            Message = "Nota de operação -> SINapse #DESLIGAR#RSGRA2_230_BC5# pelo usuario #reger+kaio.kopko#"
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<ICacheDuplicationValidator>()
            .Setup(v => v.ValidateAndSetAsync<bool>(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(true);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISinapseDadosDatasetService>()
            .Setup(s => s.GetEquipamentoDataset(It.IsAny<QueryDatasetDto>()))
            .ReturnsAsync([]);

        // Act
        await mediator.PublicarEventoAsync(mensagem, CancellationToken.None);

        // Assert
        bool eventPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEvent>();
        bool eventConsumed = await _dependencyInjectorFactory.Harness.Consumed.Any<CadastrarSolicitacaoEvent>();
        bool eventKafkaPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoSinapseEvent>();

        _dependencyInjectorFactory.Mocker.GetMock<ICacheDuplicationValidator>()
           .Verify(repository => repository.ValidateAndSetAsync<bool>(It.IsAny<string>(), It.IsAny<string>()),
           Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetService>()
           .Verify(s => s.GetEquipamentoDataset(It.IsAny<QueryDatasetDto>()),
           Times.Once);

        eventPublished.ShouldBeTrue();
        eventConsumed.ShouldBeTrue();
        eventKafkaPublished.ShouldBeFalse();
    }

    [Fact(DisplayName = "Não deve publicar mensagem se retornar mais de um dataset")]
    [Trait("EventHandler", "CadastrarSolicitacao")]
    public async Task NaoDevePublicarMensagemSeRetornarMaisDeUmDataset()
    {
        // Arrange
        IMediatorHandler mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var mensagem = new CadastrarSolicitacaoEvent
        {
            Message = "Nota de operação -> SINapse #DESLIGAR#RSGRA2_230_BC5# pelo usuario #reger+kaio.kopko#"
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<ICacheDuplicationValidator>()
            .Setup(v => v.ValidateAndSetAsync<bool>(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(true);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISinapseDadosDatasetService>()
            .Setup(s => s.GetEquipamentoDataset(It.IsAny<QueryDatasetDto>()))
            .ReturnsAsync([DatasetItemFixture.ObterDatasetItemValido(), DatasetItemFixture.ObterDatasetItemValido()]);

        // Act
        await mediator.PublicarEventoAsync(mensagem, CancellationToken.None);

        // Assert
        bool eventPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEvent>();
        bool eventConsumed = await _dependencyInjectorFactory.Harness.Consumed.Any<CadastrarSolicitacaoEvent>();
        bool eventKafkaPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoSinapseEvent>();

        _dependencyInjectorFactory.Mocker.GetMock<ICacheDuplicationValidator>()
           .Verify(repository => repository.ValidateAndSetAsync<bool>(It.IsAny<string>(), It.IsAny<string>()),
           Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetService>()
           .Verify(s => s.GetEquipamentoDataset(It.IsAny<QueryDatasetDto>()),
           Times.Once);

        eventPublished.ShouldBeTrue();
        eventConsumed.ShouldBeTrue();
        eventKafkaPublished.ShouldBeFalse();
    }
}
