using ONS.SINapse.Integracao.Sage.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.CadastrarSolicitacao;

[Collection(nameof(CadastrarSolicitacaoEventCollection))]
public partial class CadastrarSolicitacaoEventTest : IDisposable
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public CadastrarSolicitacaoEventTest()
    { 
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    public void Dispose()
    {
        _dependencyInjectorFactory.Dispose();
    }
}
