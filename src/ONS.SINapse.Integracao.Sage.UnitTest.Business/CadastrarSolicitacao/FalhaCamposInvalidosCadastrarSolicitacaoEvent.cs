using Microsoft.Extensions.DependencyInjection;
using Moq;
using ONS.SINapse.Integracao.Sage.Business.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Sage.ExternalServices.Services;
using ONS.SINapse.Integracao.Sage.Shared.DTOs;
using ONS.SINapse.Integracao.Sage.Shared.Mediator;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using Shouldly;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.CadastrarSolicitacao;
public partial class CadastrarSolicitacaoEventTest
{
    [Fact(DisplayName = "Não deve publicar mensagem se a validação do evento falhar")]
    [Trait("EventHandler", "CadastrarSolicitacao")]
    public async Task NaoDevePublicarMensagemSeValidacaoDoEventoFalhar()
    {
        // Arrange
        IMediatorHandler mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var mensagem = new CadastrarSolicitacaoEvent
        {
            Message = "Mensagem inválida"
        };

        // Act
        await mediator.PublicarEventoAsync(mensagem, CancellationToken.None);

        // Assert
        bool eventPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEvent>();
        bool eventConsumed = await _dependencyInjectorFactory.Harness.Consumed.Any<CadastrarSolicitacaoEvent>();
        bool eventKafkaPublished = await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoSinapseEvent>();

        _dependencyInjectorFactory.Mocker.GetMock<ICacheDuplicationValidator>()
           .Verify(repository => repository.ValidateAndSetAsync<bool>(It.IsAny<string>(), It.IsAny<string>()),
           Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetService>()
           .Verify(repository => repository.GetEquipamentoDataset(It.IsAny<QueryDatasetDto>()),
           Times.Never);

        eventPublished.ShouldBeTrue();
        eventConsumed.ShouldBeTrue();
        eventKafkaPublished.ShouldBeFalse();
    }

}
