using ONS.SINapse.Integracao.Sage.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.Cache.DistributedCache;

[Collection(nameof(DistributedCacheServiceCollection))]
public partial class DistributedCacheServiceTest : IDisposable
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public DistributedCacheServiceTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    public void Dispose()
    {
        _dependencyInjectorFactory.Dispose();
    }
}
