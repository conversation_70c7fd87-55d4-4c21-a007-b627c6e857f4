using Moq;
using Newtonsoft.Json;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared.Fxtures;
using Shouldly;
using StackExchange.Redis;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.Cache.DistributedCache;

public partial class DistributedCacheServiceTest
{
    [Fact(DisplayName = "Deve funcionar com fluxo completo Get/Set")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task DeveFuncionarComFluxoCompletoGetSet()
    {
        // Arrange
        string chave = "chave-fluxo-completo";
        var objetoOriginal = CacheTestFixture.ObterObjetoTesteValido();
        string jsonSerializado = JsonConvert.SerializeObject(objetoOriginal);
        int segundos = 300;
        
        var mockDatabase = _dependencyInjectorFactory.Mocker.GetMock<IDatabase>();
        
        // Setup para SetAsync
        mockDatabase
            .Setup(x => x.StringSetAsync(chave, jsonSerializado, TimeSpan.FromSeconds(segundos), When.Always, CommandFlags.None))
            .ReturnsAsync(true);
        
        // Setup para GetAsync
        mockDatabase
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(new RedisValue(jsonSerializado));

        var service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act - Set
        await service.SetAsync(chave, objetoOriginal, segundos);
        
        // Act - Get
        var objetoRecuperado = await service.GetAsync<TestCacheObject>(chave);

        // Assert
        objetoRecuperado.ShouldNotBeNull();
        objetoRecuperado.Id.ShouldBe(objetoOriginal.Id);
        objetoRecuperado.Nome.ShouldBe(objetoOriginal.Nome);
        objetoRecuperado.Valor.ShouldBe(objetoOriginal.Valor);
        objetoRecuperado.DataCriacao.ShouldBe(objetoOriginal.DataCriacao);
        objetoRecuperado.Ativo.ShouldBe(objetoOriginal.Ativo);
        objetoRecuperado.Tags.ShouldBe(objetoOriginal.Tags);
        
        // Verify calls
        mockDatabase.Verify(x => x.StringSetAsync(chave, jsonSerializado, TimeSpan.FromSeconds(segundos), When.Always, CommandFlags.None), Times.Once);
        mockDatabase.Verify(x => x.StringGetAsync(chave, CommandFlags.None), Times.Once);
    }

    [Fact(DisplayName = "Deve manter consistência entre diferentes tipos de SetAsync")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task DeveManterConsistenciaEntreDiferentesTiposDeSetAsync()
    {
        // Arrange
        string chave1 = "chave-segundos";
        string chave2 = "chave-timespan";
        var objeto = CacheTestFixture.ObterObjetoTesteValido();
        string jsonEsperado = JsonConvert.SerializeObject(objeto);
        int segundos = 600;
        var timeSpan = TimeSpan.FromSeconds(segundos);
        
        var mockDatabase = _dependencyInjectorFactory.Mocker.GetMock<IDatabase>();
        
        mockDatabase
            .Setup(x => x.StringSetAsync(chave1, jsonEsperado, TimeSpan.FromSeconds(segundos), When.Always, CommandFlags.None))
            .ReturnsAsync(true);
            
        mockDatabase
            .Setup(x => x.StringSetAsync(chave2, jsonEsperado, timeSpan, When.Always, CommandFlags.None))
            .ReturnsAsync(true);

        var service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave1, objeto, segundos);
        await service.SetAsync(chave2, objeto, timeSpan);

        // Assert
        mockDatabase.Verify(x => x.StringSetAsync(chave1, jsonEsperado, TimeSpan.FromSeconds(segundos), When.Always, CommandFlags.None), Times.Once);
        mockDatabase.Verify(x => x.StringSetAsync(chave2, jsonEsperado, timeSpan, When.Always, CommandFlags.None), Times.Once);
    }

    [Fact(DisplayName = "Deve funcionar com múltiplas operações sequenciais")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task DeveFuncionarComMultiplasOperacoesSequenciais()
    {
        // Arrange
        var objetos = new[]
        {
            new TestCacheObject { Id = "obj1", Nome = "Objeto 1", Valor = 100m },
            new TestCacheObject { Id = "obj2", Nome = "Objeto 2", Valor = 200m },
            new TestCacheObject { Id = "obj3", Nome = "Objeto 3", Valor = 300m }
        };
        
        var mockDatabase = _dependencyInjectorFactory.Mocker.GetMock<IDatabase>();
        
        // Setup para cada objeto
        for (int i = 0; i < objetos.Length; i++)
        {
            string chave = $"chave-{i + 1}";
            string json = JsonConvert.SerializeObject(objetos[i]);
            
            mockDatabase
                .Setup(x => x.StringSetAsync(chave, json, TimeSpan.FromMinutes(i + 1), When.Always, CommandFlags.None))
                .ReturnsAsync(true);
                
            mockDatabase
                .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
                .ReturnsAsync(new RedisValue(json));
        }

        var service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act & Assert
        for (int i = 0; i < objetos.Length; i++)
        {
            string chave = $"chave-{i + 1}";
            
            // Set
            await service.SetAsync(chave, objetos[i], TimeSpan.FromMinutes(i + 1));
            
            // Get
            var resultado = await service.GetAsync<TestCacheObject>(chave);
            
            // Verify
            resultado.ShouldNotBeNull();
            resultado.Id.ShouldBe(objetos[i].Id);
            resultado.Nome.ShouldBe(objetos[i].Nome);
            resultado.Valor.ShouldBe(objetos[i].Valor);
        }
        
        // Verify all calls were made
        mockDatabase.Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), When.Always, CommandFlags.None), Times.Exactly(3));
        mockDatabase.Verify(x => x.StringGetAsync(It.IsAny<RedisKey>(), CommandFlags.None), Times.Exactly(3));
    }

    [Fact(DisplayName = "Deve preservar tipos de dados complexos durante serialização/deserialização")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task DevePreservarTiposDeDadosComplexosDuranteSerializacaoDeserializacao()
    {
        // Arrange
        string chave = "chave-tipos-complexos";
        var objetoComplexo = new
        {
            Decimal = 123.456m,
            Double = 789.012d,
            Float = 345.678f,
            DateTime = new DateTime(2025, 6, 30, 15, 30, 45),
            Guid = Guid.NewGuid(),
            Enum = DayOfWeek.Monday,
            Array = new[] { 1, 2, 3, 4, 5 },
            Dictionary = new Dictionary<string, object>
            {
                { "string", "valor" },
                { "int", 42 },
                { "bool", true },
                { "null", null! }
            }
        };
        
        string jsonComplexo = JsonConvert.SerializeObject(objetoComplexo);
        
        var mockDatabase = _dependencyInjectorFactory.Mocker.GetMock<IDatabase>();
        
        mockDatabase
            .Setup(x => x.StringSetAsync(chave, jsonComplexo, TimeSpan.FromHours(1), When.Always, CommandFlags.None))
            .ReturnsAsync(true);
            
        mockDatabase
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(new RedisValue(jsonComplexo));

        var service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objetoComplexo, TimeSpan.FromHours(1));
        var resultado = await service.GetAsync<object>(chave);

        // Assert
        resultado.ShouldNotBeNull();
        
        // Verify serialization/deserialization calls
        mockDatabase.Verify(x => x.StringSetAsync(chave, jsonComplexo, TimeSpan.FromHours(1), When.Always, CommandFlags.None), Times.Once);
        mockDatabase.Verify(x => x.StringGetAsync(chave, CommandFlags.None), Times.Once);
    }
}
