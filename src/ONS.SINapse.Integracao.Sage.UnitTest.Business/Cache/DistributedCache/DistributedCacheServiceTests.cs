using Moq;
using Newtonsoft.Json;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared.Fxtures;
using Shouldly;
using StackExchange.Redis;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.Cache.DistributedCache;

public partial class DistributedCacheServiceTest
{
    [Fact(DisplayName = "GetAsync deve retornar null quando a chave não existir no Redis")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveRetornarNull_QuandoChaveNaoExistir()
    {
        // Arrange
        string chave = "chave-inexistente";
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(RedisValue.Null);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        TestCacheObject? resultado = await service.GetAsync<TestCacheObject>(chave);

        // Assert
        resultado.ShouldBeNull();
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringGetAsync(chave, CommandFlags.None), Times.Once);
    }

    [Fact(DisplayName = "GetAsync deve retornar objeto deserializado quando a chave existir")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveRetornarObjetoDeserializado_QuandoChaveExistir()
    {
        // Arrange
        string chave = "chave-existente";
        TestCacheObject objetoEsperado = CacheTestFixture.ObterObjetoTesteValido();
        string jsonEsperado = CacheTestFixture.ObterJsonObjetoTeste();
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(new RedisValue(jsonEsperado));

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        TestCacheObject? resultado = await service.GetAsync<TestCacheObject>(chave);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Id.ShouldBe(objetoEsperado.Id);
        resultado.Nome.ShouldBe(objetoEsperado.Nome);
        resultado.Valor.ShouldBe(objetoEsperado.Valor);
        resultado.DataCriacao.ShouldBe(objetoEsperado.DataCriacao);
        resultado.Ativo.ShouldBe(objetoEsperado.Ativo);
        resultado.Tags.ShouldBe(objetoEsperado.Tags);
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringGetAsync(chave, CommandFlags.None), Times.Once);
    }

    [Fact(DisplayName = "GetAsync deve retornar null quando valor no Redis for string vazia")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveRetornarNull_QuandoValorForStringVazia()
    {
        // Arrange
        string chave = "chave-vazia";
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(RedisValue.EmptyString);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        TestCacheObject? resultado = await service.GetAsync<TestCacheObject>(chave);

        // Assert
        resultado.ShouldBeNull();
    }

    [Fact(DisplayName = "GetAsync deve deserializar corretamente string simples")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveDeserializarCorretamente_StringSimples()
    {
        // Arrange
        string chave = "chave-string";
        string valorEsperado = "valor-teste";
        string jsonString = JsonConvert.SerializeObject(valorEsperado);
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(new RedisValue(jsonString));

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        string? resultado = await service.GetAsync<string>(chave);

        // Assert
        resultado.ShouldBe(valorEsperado);
    }

    [Fact(DisplayName = "SetAsync com segundos deve chamar StringSetAsync")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_ComSegundos_DeveChamarStringSetAsync()
    {
        // Arrange
        string chave = "chave-teste";
        TestCacheObject objeto = CacheTestFixture.ObterObjetoTesteValido();
        int segundos = 300;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objeto, segundos);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "SetAsync com TimeSpan deve chamar StringSetAsync")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_ComTimeSpan_DeveChamarStringSetAsync()
    {
        // Arrange
        string chave = "chave-teste";
        TestCacheObject objeto = CacheTestFixture.ObterObjetoTesteValido();
        var expiracao = TimeSpan.FromMinutes(10);
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objeto, expiracao);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "SetAsync deve serializar objeto corretamente")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_DeveSerializarObjetoCorretamente()
    {
        // Arrange
        string chave = "chave-serializacao";
        object objeto = new { Nome = "Teste", Valor = 123, Ativo = true };
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objeto, TimeSpan.Zero);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "SetAsync deve funcionar com string simples")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_DeveFuncionarComStringSimples()
    {
        // Arrange
        string chave = "chave-string";
        string valor = "valor-simples";
        int segundos = 60;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, valor, segundos);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "SetAsync deve converter zero segundos para TimeSpan.Zero")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_DeveConverterZeroSegundosParaTimeSpanZero()
    {
        // Arrange
        string chave = "chave-zero";
        TestCacheObject objeto = CacheTestFixture.ObterObjetoTesteValido();
        int segundos = 0;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objeto, segundos);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "SetAsync deve converter segundos negativos para TimeSpan negativo")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_DeveConverterSegundosNegativosParaTimeSpanNegativo()
    {
        // Arrange
        string chave = "chave-negativo";
        TestCacheObject objeto = CacheTestFixture.ObterObjetoTesteValido();
        int segundos = -10;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objeto, segundos);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "GetAsync deve retornar null quando JSON for inválido")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveRetornarNull_QuandoJsonForInvalido()
    {
        // Arrange
        string chave = "chave-json-invalido";
        string jsonInvalido = "{ invalid json }";

        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(new RedisValue(jsonInvalido));

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act & Assert
        await Should.ThrowAsync<JsonReaderException>(async () =>
            await service.GetAsync<TestCacheObject>(chave));
    }

    [Fact(DisplayName = "GetAsync deve deserializar lista de objetos corretamente")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveDeserializarListaDeObjetosCorretamente()
    {
        // Arrange
        string chave = "chave-lista";
        var listaEsperada = new List<TestCacheObject>
        {
            CacheTestFixture.ObterObjetoTesteValido(),
            new TestCacheObject { Id = "test-2", Nome = "Objeto 2", Valor = 100m, Ativo = false }
        };
        string jsonLista = JsonConvert.SerializeObject(listaEsperada);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(new RedisValue(jsonLista));

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        List<TestCacheObject>? resultado = await service.GetAsync<List<TestCacheObject>>(chave);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count.ShouldBe(2);
        resultado[0].Id.ShouldBe("test-id-123");
        resultado[1].Id.ShouldBe("test-2");
    }

    [Fact(DisplayName = "SetAsync deve serializar lista de objetos corretamente")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_DeveSerializarListaDeObjetosCorretamente()
    {
        // Arrange
        string chave = "chave-lista-set";
        var lista = new List<TestCacheObject>
        {
            CacheTestFixture.ObterObjetoTesteValido(),
            new TestCacheObject { Id = "test-2", Nome = "Objeto 2", Valor = 200m }
        };
        var expiracao = TimeSpan.FromHours(1);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, lista, expiracao);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "GetAsync deve funcionar com objeto que possui propriedades nulas")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveFuncionarComObjetoComPropriedadesNulas()
    {
        // Arrange
        string chave = "chave-objeto-nulo";
        var objetoComNulos = new TestCacheObject
        {
            Id = "test-null",
            Nome = null!,
            Tags = null!,
            Propriedades = null!
        };
        string jsonObjeto = JsonConvert.SerializeObject(objetoComNulos);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(new RedisValue(jsonObjeto));

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        TestCacheObject? resultado = await service.GetAsync<TestCacheObject>(chave);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Id.ShouldBe("test-null");
        resultado.Nome.ShouldBeNull();
        resultado.Tags.ShouldBeNull();
        resultado.Propriedades.ShouldBeNull();
    }

    [Fact(DisplayName = "SetAsync deve funcionar com grandes valores de segundos")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_DeveFuncionarComGrandesValoresDeSegundos()
    {
        // Arrange
        string chave = "chave-grande-valor";
        TestCacheObject objeto = CacheTestFixture.ObterObjetoTesteValido();
        int segundos = int.MaxValue;

        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objeto, segundos);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact(DisplayName = "GetAsync deve funcionar com chave vazia")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task GetAsync_DeveFuncionarComChaveVazia()
    {
        // Arrange
        string chave = "";

        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringGetAsync(chave, CommandFlags.None))
            .ReturnsAsync(RedisValue.Null);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        TestCacheObject? resultado = await service.GetAsync<TestCacheObject>(chave);

        // Assert
        resultado.ShouldBeNull();
    }

    [Fact(DisplayName = "SetAsync deve funcionar com chave vazia")]
    [Trait("Shared", "DistributedCacheService")]
    public async Task SetAsync_DeveFuncionarComChaveVazia()
    {
        // Arrange
        string chave = "";
        TestCacheObject objeto = CacheTestFixture.ObterObjetoTesteValido();
        int segundos = 60;

        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        DistributedCacheService service = _dependencyInjectorFactory.Mocker.CreateInstance<DistributedCacheService>();

        // Act
        await service.SetAsync(chave, objeto, segundos);

        // Assert
        _dependencyInjectorFactory.Mocker
            .GetMock<IDatabase>()
            .Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<bool>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Once);
    }
}
