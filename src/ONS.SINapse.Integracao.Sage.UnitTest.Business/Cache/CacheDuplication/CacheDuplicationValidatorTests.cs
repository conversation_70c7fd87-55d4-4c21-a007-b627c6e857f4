using Moq;
using ONS.SINapse.Integracao.Sage.Shared.Services.Caching;
using Shouldly;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.Cache.CacheDuplication;
public partial class CacheDuplicationValidatorTest
{
    [Fact(DisplayName = "Deve retornar false se a chave existir e o valor for igual")]
    [Trait("Business", "CacheDuplicationValidator")]
    public async Task DeveRetornarFalse_SeChaveExistirEValorIgual()
    {
        // Arrange
        string key = "teste-chave";
        string valor = "valor123";

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCacheService>()
            .Setup(x => x.GetAsync<string>($"SINAPSE_SAGE:{key}"))
            .ReturnsAsync(valor);

        // Act
        bool result = await _dependencyInjectorFactory.Mocker.CreateInstance<CacheDuplicationValidator>().ValidateAndSetAsync<string>(key, valor);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact(DisplayName = "Deve retornar true e salvar no cache se a chave não existir")]
    [Trait("Business", "CacheDuplicationValidator")]
    public async Task DeveRetornarTrue_SeChaveNaoExistir()
    {
        // Arrange
        string key = "nova-chave";
        string valor = "valor456";

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCacheService>()
            .Setup(x => x.GetAsync<string>(key))
            .ReturnsAsync((string?)null);

        // Act
        bool result = await _dependencyInjectorFactory.Mocker.CreateInstance<CacheDuplicationValidator>().ValidateAndSetAsync<string>(key, valor);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact(DisplayName = "Deve retornar true e sobrescrever o valor se valor for diferente")]
    [Trait("Business", "CacheDuplicationValidator")]
    public async Task DeveRetornarTrue_SeValorForDiferente()
    {
        // Arrange
        string key = "chave-existente";
        string valorExistente = "antigo";
        string novoValor = "novo";

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCacheService>()
            .Setup(x => x.GetAsync<string>(key))
            .ReturnsAsync(valorExistente);

        // Act
        bool result = await _dependencyInjectorFactory.Mocker.CreateInstance<CacheDuplicationValidator>().ValidateAndSetAsync<string>(key, novoValor);

        // Assert
        result.ShouldBeTrue();
    }
}
