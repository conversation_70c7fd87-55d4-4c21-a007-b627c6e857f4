using ONS.SINapse.Integracao.Sage.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.Integracao.Sage.UnitTest.Shared;
using Xunit;

namespace ONS.SINapse.Integracao.Sage.UnitTest.Business.Cache.CacheDuplication;

[Collection(nameof(CacheDuplicationValidatorCollection))]
public partial class CacheDuplicationValidatorTest : IDisposable
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public CacheDuplicationValidatorTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    public void Dispose()
    {
        _dependencyInjectorFactory.Dispose();
    }
}
