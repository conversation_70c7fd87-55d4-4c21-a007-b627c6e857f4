<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<solution>
		<add key="disableSourceControlIntegration" value="true" />
	</solution>

	<packageRestore>
		<add key="enabled" value="True" />
		<add key="automatic" value="True" />
	</packageRestore>

	<activePackageSource>
		<add key="All" value="(Aggregate source)" />
	</activePackageSource>

	<packageSources>
		<clear />
		<add key="ons" value="https://nuget.ons.org.br/nuget" />
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
	</packageSources>

	<packageSourceMapping>
		<packageSource key="ons">
			<package pattern="ons.*" />
		</packageSource>
		<packageSource key="nuget.org">
			<package pattern="*" />
		</packageSource>
	</packageSourceMapping>
</configuration>