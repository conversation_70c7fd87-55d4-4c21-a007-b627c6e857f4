<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Confluent.Kafka" Version="2.10.0" />
    <PackageVersion Include="MassTransit" Version="8.4.0" />
    <PackageVersion Include="MassTransit.Kafka" Version="8.4.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.31" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.WsFederation" Version="6.0.31" />
    <PackageVersion Include="Nanoid" Version="3.1.0" />
    <PackageVersion Include="OpenTelemetry" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Api" Version="1.12.0" />
    <PackageVersion Include="Refit" Version="8.0.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageVersion Include="Refit.Newtonsoft.Json" Version="8.0.0" />
    <PackageVersion Include="Scrutor" Version="6.0.1" />
    <PackageVersion Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageVersion Include="FluentValidation" Version="11.9.2" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageVersion Include="LinqKit" Version="1.3.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.15" />
    <!--  OpenTelemetry start  -->
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.2" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.11.2" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.11.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.9.0-beta.2" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Process" Version="1.11.0-beta.2" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.StackExchangeRedis" Version="1.11.0-beta.2" />
    <!--  Sonar  -->
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="10.8.0.113526" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <!--  ONS Confgit start  -->
    <PackageVersion Include="ons.configit.entidade" Version="1.0.0" />
    <PackageVersion Include="Simple.OData.Client" Version="6.0.1" />
    <!--  Health check start  -->
    <PackageVersion Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Core" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="8.0.1" />
    <!--   Test   -->
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="MassTransit.Newtonsoft" Version="8.3.2" />
    <PackageVersion Include="Moq.AutoMock" Version="3.5.0" />
    <PackageVersion Include="Shouldly" Version="4.2.1" />
  </ItemGroup>
</Project>